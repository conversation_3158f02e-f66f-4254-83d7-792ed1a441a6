#!/usr/bin/env python3
"""
Test script for Docling Web UI

This script tests the web UI functionality without starting the full server.
"""

import sys
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_imports():
    """Test that all required modules can be imported"""
    print("🧪 Testing imports...")
    
    try:
        import fastapi
        print("✅ FastAPI imported successfully")
    except ImportError as e:
        print(f"❌ FastAPI import failed: {e}")
        return False
    
    try:
        import jinja2
        print("✅ Jinja2 imported successfully")
    except ImportError as e:
        print(f"❌ Jinja2 import failed: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn imported successfully")
    except ImportError as e:
        print(f"❌ Uvicorn import failed: {e}")
        return False
    
    try:
        from docling.document_converter import DocumentConverter
        print("✅ Docling DocumentConverter imported successfully")
    except ImportError as e:
        print(f"❌ Docling import failed: {e}")
        return False
    
    return True

def test_file_structure():
    """Test that all required files exist"""
    print("\n📁 Testing file structure...")
    
    required_files = [
        "api/main.py",
        "api/templates/index.html",
        "api/static/style.css",
        "api/static/script.js"
    ]
    
    all_exist = True
    for file_path in required_files:
        path = current_dir / file_path
        if path.exists():
            print(f"✅ {file_path} exists")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    return all_exist

def test_api_creation():
    """Test that the FastAPI app can be created"""
    print("\n🚀 Testing API creation...")
    
    try:
        # Import the app
        from api.main import app
        print("✅ FastAPI app created successfully")
        
        # Check routes
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/process-document/", "/health", "/api/formats"]
        
        for route in expected_routes:
            if route in routes:
                print(f"✅ Route {route} registered")
            else:
                print(f"❌ Route {route} missing")
                return False
        
        return True
    except Exception as e:
        print(f"❌ API creation failed: {e}")
        return False

def test_template_rendering():
    """Test that templates can be rendered"""
    print("\n🎨 Testing template rendering...")
    
    try:
        from fastapi.templating import Jinja2Templates
        from fastapi import Request
        
        templates_dir = current_dir / "api" / "templates"
        templates = Jinja2Templates(directory=str(templates_dir))
        
        # Create a mock request
        class MockRequest:
            def __init__(self):
                self.url = "http://localhost:8081/"
        
        request = MockRequest()
        
        # Try to render the template
        response = templates.TemplateResponse("index.html", {"request": request})
        print("✅ Template rendering successful")
        return True
    except Exception as e:
        print(f"❌ Template rendering failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Docling Web UI Test Suite")
    print("=" * 40)
    
    tests = [
        ("Import Test", test_imports),
        ("File Structure Test", test_file_structure),
        ("API Creation Test", test_api_creation),
        ("Template Rendering Test", test_template_rendering)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Running {test_name}...")
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print("\n" + "=" * 40)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Web UI is ready to start.")
        print("\n🚀 To start the web UI, run:")
        print("   python start_web_ui.py")
        print("\n🌐 Then visit: http://localhost:8081")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
