// Modern Docling UI JavaScript with Cancel and Cloud Save

class DoclingUI {
    constructor() {
        this.currentRequest = null;
        this.processingStartTime = null;
        this.processingTimer = null;
        this.currentResult = null;
        this.isDarkMode = true;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupFileUpload();
        this.checkHealth();
        this.initializeGoogleDrive();
    }

    setupEventListeners() {
        // Form submission
        document.getElementById('uploadForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.processDocument();
        });

        // Cancel button
        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.cancelProcessing();
        });

        // Theme toggle
        document.querySelector('[onclick="toggleTheme()"]').addEventListener('click', () => {
            this.toggleTheme();
        });

        // Health check
        document.querySelector('[onclick="checkHealth()"]').addEventListener('click', () => {
            this.checkHealth();
        });
    }

    setupFileUpload() {
        const fileUploadArea = document.getElementById('fileUploadArea');
        const fileInput = document.getElementById('fileInput');

        // Drag and drop
        fileUploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileUploadArea.classList.add('dragover');
        });

        fileUploadArea.addEventListener('dragleave', () => {
            fileUploadArea.classList.remove('dragover');
        });

        fileUploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            fileUploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                this.updateFileDisplay(files[0]);
            }
        });

        // File input change
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                this.updateFileDisplay(e.target.files[0]);
            }
        });

        // Click to upload
        fileUploadArea.addEventListener('click', () => {
            fileInput.click();
        });
    }

    updateFileDisplay(file) {
        const uploadContent = document.querySelector('.upload-content');
        const fileName = file.name;
        const fileSize = this.formatFileSize(file.size);
        
        uploadContent.innerHTML = `
            <div class="upload-icon">
                <i class="fas fa-file-check"></i>
            </div>
            <div class="upload-text">
                <h3>${fileName}</h3>
                <p>Size: ${fileSize} • Ready to process</p>
            </div>
            <div class="upload-formats">
                <span class="format-tag selected">Ready</span>
            </div>
        `;
    }

    async processDocument() {
        const formData = new FormData();
        const fileInput = document.getElementById('fileInput');
        const file = fileInput.files[0];

        if (!file) {
            this.showError('Please select a file to process.');
            return;
        }

        // Prepare form data
        formData.append('file', file);
        formData.append('enable_ocr', document.getElementById('enableOcr').checked);
        formData.append('enable_table_structure', document.getElementById('enableTableStructure').checked);
        
        const outputFormat = document.querySelector('input[name="output_format"]:checked').value;
        formData.append('output_format', outputFormat);

        // Show progress
        this.showProgress();
        this.startProcessingTimer();

        try {
            // Use timeout-enabled server
            const response = await fetch('/process-document/', {
                method: 'POST',
                body: formData,
                signal: this.createAbortSignal()
            });

            this.currentRequest = response;

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                this.showResults(result);
                this.currentResult = result;
            } else {
                this.showError(result.error || 'Processing failed');
            }

        } catch (error) {
            if (error.name === 'AbortError') {
                this.showError('Processing was cancelled by user.');
            } else {
                this.showError(`Processing failed: ${error.message}`);
            }
        } finally {
            this.hideProgress();
            this.stopProcessingTimer();
        }
    }

    createAbortSignal() {
        const controller = new AbortController();
        this.currentAbortController = controller;
        return controller.signal;
    }

    cancelProcessing() {
        if (this.currentAbortController) {
            this.currentAbortController.abort();
            this.currentAbortController = null;
        }
        
        this.hideProgress();
        this.stopProcessingTimer();
        this.showError('Processing cancelled by user.');
    }

    showProgress() {
        document.getElementById('progressContainer').classList.add('show');
        document.getElementById('processBtn').disabled = true;
        document.getElementById('cancelBtn').disabled = false;
        
        // Animate progress steps
        this.animateProgressSteps();
    }

    hideProgress() {
        document.getElementById('progressContainer').classList.remove('show');
        document.getElementById('processBtn').disabled = false;
        document.getElementById('cancelBtn').disabled = true;
    }

    animateProgressSteps() {
        const steps = ['step1', 'step2', 'step3', 'step4'];
        let currentStep = 0;

        const interval = setInterval(() => {
            if (currentStep < steps.length) {
                document.getElementById(steps[currentStep]).classList.add('active');
                currentStep++;
            } else {
                clearInterval(interval);
            }
        }, 1000);

        this.progressInterval = interval;
    }

    startProcessingTimer() {
        this.processingStartTime = Date.now();
        this.processingTimer = setInterval(() => {
            const elapsed = Date.now() - this.processingStartTime;
            const minutes = Math.floor(elapsed / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            document.getElementById('processingTime').textContent = 
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopProcessingTimer() {
        if (this.processingTimer) {
            clearInterval(this.processingTimer);
            this.processingTimer = null;
        }
        if (this.progressInterval) {
            clearInterval(this.progressInterval);
            this.progressInterval = null;
        }
    }

    showResults(result) {
        // Show results section
        document.querySelector('.results-section').classList.add('show');
        document.getElementById('saveOptions').classList.add('show');

        // Update stats
        this.updateStats(result);

        // Show content
        this.displayContent(result.content, result.output_format);
    }

    updateStats(result) {
        const statsGrid = document.getElementById('documentStats');
        statsGrid.innerHTML = `
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
                <div class="stat-value">${result.page_count || 0}</div>
                <div class="stat-label">Pages</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-table"></i></div>
                <div class="stat-value">${result.document_structure?.tables_count || 0}</div>
                <div class="stat-label">Tables</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-image"></i></div>
                <div class="stat-value">${result.document_structure?.figures_count || 0}</div>
                <div class="stat-label">Figures</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                <div class="stat-value">${result.processing_info?.processing_time || 'N/A'}</div>
                <div class="stat-label">Processing Time</div>
            </div>
        `;
    }

    displayContent(content, format) {
        const resultContent = document.getElementById('resultContent');
        resultContent.innerHTML = `
            <div class="content-header">
                <h3>Extracted Content (${format.toUpperCase()})</h3>
                <div class="content-actions">
                    <button class="btn-ghost" onclick="doclingUI.copyToClipboard()">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
            </div>
            <div class="content-body">
                <pre><code>${this.escapeHtml(content)}</code></pre>
            </div>
        `;
    }

    showError(message) {
        const errorDisplay = document.getElementById('errorAlert');
        document.getElementById('errorMessage').textContent = message;
        errorDisplay.classList.add('show');
        
        // Auto-hide after 10 seconds
        setTimeout(() => {
            this.hideError();
        }, 10000);
    }

    hideError() {
        document.getElementById('errorAlert').classList.remove('show');
    }

    async checkHealth() {
        const statusElement = document.getElementById('healthStatus');
        statusElement.textContent = 'Checking...';
        
        try {
            const response = await fetch('/health');
            const health = await response.json();
            
            if (health.status === 'healthy') {
                statusElement.textContent = 'Healthy';
                statusElement.style.color = 'var(--success)';
            } else {
                statusElement.textContent = 'Unhealthy';
                statusElement.style.color = 'var(--error)';
            }
        } catch (error) {
            statusElement.textContent = 'Error';
            statusElement.style.color = 'var(--error)';
        }
    }

    // Cloud Save Functions
    async saveToGoogleDrive() {
        if (!this.currentResult) return;
        
        try {
            // Initialize Google Drive API
            await this.initializeGoogleDrive();
            
            const fileName = `${this.currentResult.filename}_processed.${this.currentResult.output_format}`;
            const content = this.currentResult.content;
            
            // Create file metadata
            const metadata = {
                name: fileName,
                parents: ['root']
            };
            
            // Upload to Google Drive
            const form = new FormData();
            form.append('metadata', new Blob([JSON.stringify(metadata)], {type: 'application/json'}));
            form.append('file', new Blob([content], {type: 'text/plain'}));
            
            const response = await fetch('https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart', {
                method: 'POST',
                headers: new Headers({
                    'Authorization': `Bearer ${gapi.auth2.getAuthInstance().currentUser.get().getAuthResponse().access_token}`
                }),
                body: form
            });
            
            if (response.ok) {
                this.showSuccess('File saved to Google Drive successfully!');
            } else {
                throw new Error('Failed to save to Google Drive');
            }
        } catch (error) {
            this.showError(`Failed to save to Google Drive: ${error.message}`);
        }
    }

    // Utility Functions
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    toggleTheme() {
        // Theme toggle functionality (placeholder)
        this.isDarkMode = !this.isDarkMode;
        document.getElementById('themeIcon').className = this.isDarkMode ? 'fas fa-moon' : 'fas fa-sun';
    }

    async initializeGoogleDrive() {
        // Google Drive API initialization (placeholder)
        // You would need to set up Google API credentials
        console.log('Google Drive API initialization...');
    }

    copyToClipboard() {
        if (this.currentResult && this.currentResult.content) {
            navigator.clipboard.writeText(this.currentResult.content).then(() => {
                this.showSuccess('Content copied to clipboard!');
            }).catch(() => {
                this.showError('Failed to copy to clipboard');
            });
        }
    }

    downloadResult() {
        if (!this.currentResult) return;
        
        const fileName = `${this.currentResult.filename}_processed.${this.currentResult.output_format}`;
        const content = this.currentResult.content;
        const blob = new Blob([content], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        this.showSuccess('File downloaded successfully!');
    }

    showSuccess(message) {
        // Create success notification
        const notification = document.createElement('div');
        notification.className = 'success-notification';
        notification.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <span>${message}</span>
        `;
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the UI when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.doclingUI = new DoclingUI();
});

// Legacy function support
function checkHealth() {
    window.doclingUI.checkHealth();
}

function toggleTheme() {
    window.doclingUI.toggleTheme();
}

function copyToClipboard() {
    window.doclingUI.copyToClipboard();
}

function downloadResult() {
    window.doclingUI.downloadResult();
}

function saveToGoogleDrive() {
    window.doclingUI.saveToGoogleDrive();
}

function saveToDropbox() {
    // Dropbox save functionality (placeholder)
    console.log('Dropbox save functionality');
}

function hideError() {
    window.doclingUI.hideError();
}
