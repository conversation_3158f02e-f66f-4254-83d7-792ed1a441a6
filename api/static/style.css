/* Custom CSS for Docling Web UI */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: 1px solid rgba(0,0,0,0.125);
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #dee2e6;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* <PERSON><PERSON> Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #**********%);
    transform: translateY(-1px);
}

/* Progress Bar */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
}

/* Results Container */
#resultsContainer {
    max-height: 600px;
    overflow-y: auto;
    border-radius: 8px;
}

.result-content {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.result-content.html-content {
    font-family: inherit;
    white-space: normal;
}

.result-content.json-content {
    background-color: #2d3748;
    color: #e2e8f0;
    border: 1px solid #4a5568;
}

/* Statistics */
.stat-item {
    padding: 15px;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Document Info Styles */
.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: #495057;
}

.info-value {
    color: #6c757d;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File Input Styling */
.form-control[type="file"] {
    padding: 8px 12px;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    margin-right: 10px;
    cursor: pointer;
    font-weight: 500;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background: linear-gradient(135deg, #0056b3 0%, #**********%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0 10px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .result-content {
        font-size: 12px;
        padding: 15px;
    }
}

/* Health Status */
#healthStatus {
    font-weight: 500;
}

#healthStatus.healthy {
    color: #28a745;
}

#healthStatus.unhealthy {
    color: #dc3545;
}

/* Syntax Highlighting for JSON */
.json-key {
    color: #0066cc;
    font-weight: bold;
}

.json-string {
    color: #009900;
}

.json-number {
    color: #cc6600;
}

.json-boolean {
    color: #cc0066;
    font-weight: bold;
}

.json-null {
    color: #999999;
    font-weight: bold;
}

/* Copy Success Animation */
.copy-success {
    animation: copyFlash 0.5s ease-in-out;
}

@keyframes copyFlash {
    0% { background-color: transparent; }
    50% { background-color: #d4edda; }
    100% { background-color: transparent; }
}

/* Footer */
footer {
    margin-top: auto;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
