// Bidbees Web UI JavaScript - Optimized for Large Documents

let currentResult = null;
let currentFormat = 'markdown';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Check health status on load
    checkHealth();
    
    // Setup form submission
    const uploadForm = document.getElementById('uploadForm');
    uploadForm.addEventListener('submit', handleFormSubmit);
    
    // Setup file input change handler
    const fileInput = document.getElementById('fileInput');
    fileInput.addEventListener('change', handleFileSelect);
    
    // Setup output format change handler
    const outputFormat = document.getElementById('outputFormat');
    outputFormat.addEventListener('change', handleFormatChange);
}

async function checkHealth() {
    const healthStatus = document.getElementById('healthStatus');
    
    try {
        const response = await fetch('/health');
        const data = await response.json();
        
        if (data.status === 'healthy') {
            healthStatus.textContent = 'Online';
            healthStatus.className = 'healthy';
        } else {
            healthStatus.textContent = 'Issues Detected';
            healthStatus.className = 'unhealthy';
        }
    } catch (error) {
        healthStatus.textContent = 'Offline';
        healthStatus.className = 'unhealthy';
        console.error('Health check failed:', error);
    }
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        // Reset previous results
        resetResults();
        
        // Show file info
        console.log('Selected file:', file.name, 'Size:', formatFileSize(file.size));
    }
}

function handleFormatChange(event) {
    currentFormat = event.target.value;
    
    // If we have current results, re-render them in the new format
    if (currentResult && currentResult.content) {
        displayResults(currentResult);
    }
}

async function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const file = formData.get('file');
    
    if (!file || file.size === 0) {
        showError('Please select a file to process');
        return;
    }
    
    // Show processing state
    showProcessing(true);
    hideError();
    resetResults();
    
    try {
        const response = await fetch('/process-document/', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentResult = result;
            displayResults(result);
            updateStatistics(result);
            showDocumentInfo(result);
        } else {
            showError(result.error || 'Processing failed');
        }
    } catch (error) {
        console.error('Processing error:', error);
        showError('Network error: ' + error.message);
    } finally {
        showProcessing(false);
    }
}

function displayResults(result) {
    const resultsContainer = document.getElementById('resultsContainer');
    const resultActions = document.getElementById('resultActions');
    
    let content = result.content;
    let contentClass = 'result-content';
    
    // Format content based on output format
    if (result.output_format === 'json') {
        content = formatJSON(content);
        contentClass += ' json-content';
    } else if (result.output_format === 'html') {
        contentClass += ' html-content';
    }
    
    resultsContainer.innerHTML = `
        <div class="${contentClass}" id="resultContent">${content}</div>
    `;
    
    // Show action buttons
    resultActions.style.display = 'block';
}

function formatJSON(jsonString) {
    try {
        const parsed = JSON.parse(jsonString);
        return JSON.stringify(parsed, null, 2);
    } catch (e) {
        return jsonString;
    }
}

function updateStatistics(result) {
    const stats = result.document_structure;
    
    document.getElementById('pageCount').textContent = result.page_count || 0;
    document.getElementById('tableCount').textContent = stats.tables_count || 0;
    document.getElementById('figureCount').textContent = stats.figures_count || 0;
    document.getElementById('textItemCount').textContent = stats.text_items_count || 0;
}

function showDocumentInfo(result) {
    const documentInfo = document.getElementById('documentInfo');
    const documentInfoContent = document.getElementById('documentInfoContent');
    
    const processingInfo = result.processing_info;
    
    documentInfoContent.innerHTML = `
        <div class="info-item">
            <span class="info-label">Filename:</span>
            <span class="info-value">${result.filename}</span>
        </div>
        <div class="info-item">
            <span class="info-label">File Size:</span>
            <span class="info-value">${formatFileSize(result.file_size)}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Content Type:</span>
            <span class="info-value">${result.content_type}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Output Format:</span>
            <span class="info-value">${result.output_format.toUpperCase()}</span>
        </div>
        <div class="info-item">
            <span class="info-label">OCR Enabled:</span>
            <span class="info-value">${processingInfo.ocr_enabled ? 'Yes' : 'No'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Table Structure:</span>
            <span class="info-value">${processingInfo.table_structure_enabled ? 'Yes' : 'No'}</span>
        </div>
        <div class="info-item">
            <span class="info-label">Status:</span>
            <span class="info-value">${processingInfo.conversion_status}</span>
        </div>
    `;
    
    documentInfo.style.display = 'block';
}

function showProcessing(show) {
    const processBtn = document.getElementById('processBtn');
    const progressContainer = document.getElementById('progressContainer');
    
    if (show) {
        processBtn.disabled = true;
        processBtn.innerHTML = '<span class="loading-spinner me-2"></span>Processing...';
        progressContainer.style.display = 'block';
    } else {
        processBtn.disabled = false;
        processBtn.innerHTML = '<i class="fas fa-cogs me-2"></i>Process Document';
        progressContainer.style.display = 'none';
    }
}

function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');
    
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    
    // Auto-hide after 10 seconds
    setTimeout(() => {
        hideError();
    }, 10000);
}

function hideError() {
    const errorAlert = document.getElementById('errorAlert');
    errorAlert.style.display = 'none';
}

function resetResults() {
    const resultsContainer = document.getElementById('resultsContainer');
    const resultActions = document.getElementById('resultActions');
    const documentInfo = document.getElementById('documentInfo');
    
    resultsContainer.innerHTML = `
        <div class="text-center text-muted py-5">
            <i class="fas fa-file-upload fa-3x mb-3"></i>
            <p>Upload and process a document to see results here</p>
        </div>
    `;
    
    resultActions.style.display = 'none';
    documentInfo.style.display = 'none';
    
    // Reset statistics
    document.getElementById('pageCount').textContent = '-';
    document.getElementById('tableCount').textContent = '-';
    document.getElementById('figureCount').textContent = '-';
    document.getElementById('textItemCount').textContent = '-';
}

async function copyToClipboard() {
    const resultContent = document.getElementById('resultContent');
    
    if (resultContent) {
        try {
            await navigator.clipboard.writeText(resultContent.textContent);
            
            // Show success feedback
            resultContent.classList.add('copy-success');
            setTimeout(() => {
                resultContent.classList.remove('copy-success');
            }, 500);
            
            // Show temporary success message
            showTemporaryMessage('Content copied to clipboard!', 'success');
        } catch (error) {
            console.error('Copy failed:', error);
            showTemporaryMessage('Failed to copy content', 'error');
        }
    }
}

function downloadResult() {
    if (!currentResult) return;
    
    const content = currentResult.content;
    const filename = currentResult.filename;
    const format = currentResult.output_format;
    
    // Determine file extension
    let extension = '.txt';
    let mimeType = 'text/plain';
    
    if (format === 'markdown') {
        extension = '.md';
        mimeType = 'text/markdown';
    } else if (format === 'html') {
        extension = '.html';
        mimeType = 'text/html';
    } else if (format === 'json') {
        extension = '.json';
        mimeType = 'application/json';
    }
    
    // Create download
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    
    a.href = url;
    a.download = filename.replace(/\.[^/.]+$/, '') + '_processed' + extension;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    showTemporaryMessage('File downloaded successfully!', 'success');
}

function showTemporaryMessage(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
    
    const messageDiv = document.createElement('div');
    messageDiv.className = `alert ${alertClass} position-fixed`;
    messageDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    messageDiv.innerHTML = `<i class="${icon} me-2"></i>${message}`;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
