/* Modern Futuristic UI - Vercel Style */

:root {
    /* Colors */
    --primary: #0070f3;
    --primary-dark: #0051cc;
    --secondary: #7c3aed;
    --success: #00d084;
    --warning: #f59e0b;
    --error: #ef4444;
    --gray-50: #fafafa;
    --gray-100: #f5f5f5;
    --gray-200: #e5e5e5;
    --gray-300: #d4d4d4;
    --gray-400: #a3a3a3;
    --gray-500: #737373;
    --gray-600: #525252;
    --gray-700: #404040;
    --gray-800: #262626;
    --gray-900: #171717;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --gradient-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    
    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-12: 3rem;
    
    /* Border radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.4);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #000;
    color: var(--gray-100);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Background Effects */
.bg-gradient {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-bg);
    opacity: 0.1;
    z-index: -2;
}

.bg-grid {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
        linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -1;
}

/* Navigation */
.navbar {
    position: sticky;
    top: 0;
    z-index: 100;
    backdrop-filter: blur(20px);
    background: rgba(0, 0, 0, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--space-4) 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.brand-icon {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.brand-text h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    margin: 0;
}

.brand-text span {
    font-size: 0.875rem;
    color: var(--gray-400);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

/* Main Content */
.main-content {
    min-height: 100vh;
    padding: var(--space-8) 0;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-6);
}

/* Hero Section */
.hero-section {
    text-align: center;
    margin-bottom: var(--space-12);
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-6);
    background: linear-gradient(135deg, #fff 0%, #a3a3a3 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--gray-400);
    max-width: 600px;
    margin: 0 auto;
}

/* Processing Panel */
.processing-panel {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-2xl);
    padding: var(--space-8);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-xl);
}

.panel-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
}

/* Upload Section */
.upload-section {
    display: flex;
    flex-direction: column;
    gap: var(--space-6);
}

.section-header {
    text-align: center;
}

.section-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    margin: 0 auto var(--space-4);
}

.section-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--space-2);
    color: white;
}

.section-header p {
    color: var(--gray-400);
    font-size: 0.875rem;
}

/* File Upload */
.file-upload-area {
    position: relative;
    border: 2px dashed rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.02);
}

.file-upload-area:hover {
    border-color: var(--primary);
    background: rgba(0, 112, 243, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--primary);
    background: rgba(0, 112, 243, 0.1);
    transform: scale(1.02);
}

.file-upload-area input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-content {
    pointer-events: none;
}

.upload-icon {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: var(--space-4);
}

.upload-text h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: white;
    margin-bottom: var(--space-2);
}

.upload-text p {
    color: var(--gray-400);
    font-size: 0.875rem;
}

.upload-link {
    color: var(--primary);
    text-decoration: underline;
}

.upload-formats {
    display: flex;
    justify-content: center;
    gap: var(--space-2);
    margin-top: var(--space-4);
}

.format-tag {
    background: rgba(255, 255, 255, 0.1);
    color: var(--gray-300);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-md);
    font-size: 0.75rem;
    font-weight: 500;
}

/* Hidden elements */
.progress-section,
.results-section,
.save-options,
.error-display {
    display: none;
}

.progress-section.show,
.results-section.show,
.save-options.show,
.error-display.show {
    display: block;
}

@media (max-width: 768px) {
    .panel-grid {
        grid-template-columns: 1fr;
        gap: var(--space-6);
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .container {
        padding: 0 var(--space-4);
    }
}
