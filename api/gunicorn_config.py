import multiprocessing
import os

# Server socket
bind = "0.0.0.0:8081"

# Worker processes
workers = min(multiprocessing.cpu_count() * 2 + 1, 4)  # Limit to 4 workers
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 300  # Increased timeout to 5 minutes
keepalive = 2

# Logging
accesslog = "logs/access.log"
errorlog = "logs/error.log"
loglevel = "info"

# Process naming
proc_name = "docling_api"

# SSL
keyfile = None  # Set this to your SSL key file path
certfile = None  # Set this to your SSL certificate file path

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Graceful shutdown
graceful_timeout = 30

# File upload settings
max_request_line = 8190
max_request_fields = 100
max_request_field_size = 8190

# Update the wsgi_app path in gunicorn_config.py
# Change this line
wsgi_app = "api.main:app"
# To
wsgi_app = "api.api.api.main_prod:app"
