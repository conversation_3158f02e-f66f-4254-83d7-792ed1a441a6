import tempfile
from fastapi import FastAPI, File, UploadFile, HTTPException
from docling.document_converter import DocumentConverter

app = FastAPI(
    title="Docling API",
    description="An API to process documents using the Docling SDK.",
    version="1.0.0"
)

converter = DocumentConverter()

@app.post("/process-document/")
async def process_document(file: UploadFile = File(...)):
    """
    Accepts a document file, processes it with <PERSON><PERSON>,
    and returns the extracted document structure as JSON.
    """
    try:
        with tempfile.NamedTemporaryFile(delete=True, suffix=file.filename) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            result = converter.convert(temp_file.name)
            document_dict = result.document.export_to_dict()
            return document_dict
    except Exception as e:
        print(f"Error processing file: {e}")
        raise HTTPException(status_code=500, detail=f"An error occurred: {str(e)}")

@app.get("/")
def read_root():
    return {"status": "Docling API is running"}

source = "/Users/<USER>/Downloads/Letter to Bidder -Inxa.pdf"
converter = DocumentConverter()
result = converter.convert(source)
print(result.document.export_to_markdown())