import multiprocessing
import os
from pathlib import Path

# Server socket
bind = "0.0.0.0:8080"
backlog = 2048

# Worker processes
workers = min(multiprocessing.cpu_count() * 2 + 1, 4)
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
timeout = 300
keepalive = 2

# Logging
log_dir = Path("api/logs")
log_dir.mkdir(exist_ok=True)
accesslog = str(log_dir / "access.log")
errorlog = str(log_dir / "error.log")
loglevel = "debug"

# Process naming
proc_name = "docling_api"

# Application
wsgi_app = "app.main:app"
