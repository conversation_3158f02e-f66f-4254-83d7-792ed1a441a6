import tempfile
import logging
import warnings
import re
from contextlib import asynccontextmanager
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from docling.document_converter import DocumentConverter
from typing import Dict, Any, List
import asyncio
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('docling_api.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Suppress specific warnings
warnings.filterwarnings('ignore', category=RuntimeWarning, module='numpy')
warnings.filterwarnings('ignore', category=UserWarning, module='multiprocessing.resource_tracker')

# Global converter instance
converter = None

# Configure processing limits
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
MAX_PAGES = 1000
PROCESSING_TIMEOUT = 300  # 5 minutes

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global converter
    try:
        logger.info("Initializing Docling converter...")
        converter = DocumentConverter()
        logger.info("Docling converter initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Docling converter: {e}")
        raise
    yield
    # Shutdown
    logger.info("Shutting down Docling API...")
    if converter:
        del converter

app = FastAPI(
    title="Docling API",
    description="An API to process documents using the Docling SDK.",
    version="1.0.0",
    lifespan=lifespan
)

def clean_text(text: str) -> str:
    """Clean text by removing extra spaces and normalizing whitespace."""
    # Remove multiple spaces
    text = re.sub(r'\s+', ' ', text)
    # Remove leading/trailing whitespace
    text = text.strip()
    return text

def clean_coordinates(bbox: Dict[str, float]) -> Dict[str, float]:
    """Round coordinates to 2 decimal places."""
    return {k: round(v, 2) for k, v in bbox.items()}

def clean_document_data(document_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Clean the entire document data structure."""
    cleaned = document_dict.copy()
    
    # Clean texts
    if 'texts' in cleaned:
        for text in cleaned['texts']:
            if 'text' in text:
                text['text'] = clean_text(text['text'])
            if 'orig' in text:
                text['orig'] = clean_text(text['orig'])
            if 'prov' in text:
                for prov in text['prov']:
                    if 'bbox' in prov:
                        prov['bbox'] = clean_coordinates(prov['bbox'])
    
    # Clean pictures
    if 'pictures' in cleaned:
        for picture in cleaned['pictures']:
            if 'prov' in picture:
                for prov in picture['prov']:
                    if 'bbox' in prov:
                        prov['bbox'] = clean_coordinates(prov['bbox'])
    
    # Clean groups
    if 'groups' in cleaned:
        for group in cleaned['groups']:
            if 'label' in group:
                group['label'] = clean_text(group['label'])
    
    return cleaned

async def validate_file(file: UploadFile) -> None:
    """Validate file size and type."""
    if file.size > MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Maximum size is {MAX_FILE_SIZE/1024/1024}MB"
        )
    
    if not file.filename.lower().endswith(('.pdf', '.doc', '.docx')):
        raise HTTPException(
            status_code=400,
            detail="Only PDF and Word documents are supported"
        )

async def process_document_in_background(file_path: str) -> Dict[str, Any]:
    """Process document in background with timeout."""
    try:
        result = await asyncio.wait_for(
            asyncio.to_thread(converter.convert, file_path),
            timeout=PROCESSING_TIMEOUT
        )
        return result.document.export_to_dict()
    except asyncio.TimeoutError:
        raise HTTPException(
            status_code=504,
            detail="Document processing timed out"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing document: {str(e)}"
        )

@app.post("/process-document/")
async def process_document(file: UploadFile = File(...)):
    """
    Accepts a document file, processes it with Docling,
    and returns the extracted document structure as JSON.
    """
    try:
        logger.info(f"Processing document: {file.filename}")
        with tempfile.NamedTemporaryFile(delete=True, suffix=file.filename) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            result = converter.convert(temp_file.name)
            document_dict = result.document.export_to_dict()
            
            logger.info(f"Successfully processed document: {file.filename}")
            return document_dict
    except Exception as e:
        logger.error(f"Error processing file {file.filename}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while processing the document: {str(e)}"
        )

@app.post("/process-document/clean")
async def process_document_clean(file: UploadFile = File(...)):
    """
    Accepts a document file, processes it with Docling,
    cleans the data, and returns the cleaned document structure as JSON.
    """
    try:
        logger.info(f"Processing and cleaning document: {file.filename}")
        with tempfile.NamedTemporaryFile(delete=True, suffix=file.filename) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file.flush()
            
            result = converter.convert(temp_file.name)
            document_dict = result.document.export_to_dict()
            
            # Clean the document data
            cleaned_document = clean_document_data(document_dict)
            
            logger.info(f"Successfully processed and cleaned document: {file.filename}")
            return cleaned_document
    except Exception as e:
        logger.error(f"Error processing file {file.filename}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"An error occurred while processing the document: {str(e)}"
        )

@app.get("/")
def read_root():
    return {
        "status": "Docling API is running",
        "version": "1.0.0"
    }

@app.get("/health")
def health_check():
    return {
        "status": "healthy",
        "converter_initialized": converter is not None
    }
