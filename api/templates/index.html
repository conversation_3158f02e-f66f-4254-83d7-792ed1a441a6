<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bidbees Web UI - Advanced Document Processing</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/style.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <header class="bg-primary text-white py-3 mb-4">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="mb-0">
                            <i class="fas fa-file-alt me-2"></i>
                            Bidbees Web UI
                        </h1>
                        <p class="mb-0 opacity-75">Enterprise Document Processing for Large Documents (300-400 Pages)</p>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-light" onclick="checkHealth()">
                            <i class="fas fa-heartbeat me-1"></i>
                            Health Check
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="container">
            <div class="row">
                <!-- Upload Section -->
                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">
                                <i class="fas fa-upload me-2"></i>
                                Upload Document
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <!-- File Upload -->
                                <div class="mb-3">
                                    <label for="fileInput" class="form-label">Select Document</label>
                                    <input type="file" class="form-control" id="fileInput" name="file" 
                                           accept=".pdf,.docx,.pptx,.xlsx,.html,.md,.csv,.png,.jpg,.jpeg" required>
                                    <div class="form-text">
                                        Supported formats: PDF, DOCX, PPTX, XLSX, HTML, Markdown, CSV, Images<br>
                                        <strong>Optimized for large documents (300-400 pages)</strong>
                                    </div>
                                </div>

                                <!-- Processing Options -->
                                <div class="mb-3">
                                    <label class="form-label">Processing Options</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableOcr" name="enable_ocr">
                                        <label class="form-check-label" for="enableOcr">
                                            Enable OCR (Optical Character Recognition)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableTableStructure" 
                                               name="enable_table_structure" checked>
                                        <label class="form-check-label" for="enableTableStructure">
                                            Enable Table Structure Detection
                                        </label>
                                    </div>
                                </div>

                                <!-- Output Format -->
                                <div class="mb-3">
                                    <label for="outputFormat" class="form-label">Output Format</label>
                                    <select class="form-select" id="outputFormat" name="output_format">
                                        <option value="markdown" selected>Markdown</option>
                                        <option value="html">HTML</option>
                                        <option value="json">JSON</option>
                                    </select>
                                </div>

                                <!-- Submit Button -->
                                <button type="submit" class="btn btn-primary w-100" id="processBtn">
                                    <i class="fas fa-cogs me-2"></i>
                                    Process Document
                                </button>
                            </form>

                            <!-- Progress Bar -->
                            <div class="mt-3" id="progressContainer" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                         role="progressbar" style="width: 100%">
                                        Processing...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Document Info -->
                    <div class="card shadow-sm mt-4" id="documentInfo" style="display: none;">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Document Information
                            </h6>
                        </div>
                        <div class="card-body" id="documentInfoContent">
                            <!-- Content will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Results Section -->
                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-text me-2"></i>
                                Processing Results
                            </h5>
                            <div class="btn-group" role="group" id="resultActions" style="display: none;">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyToClipboard()">
                                    <i class="fas fa-copy me-1"></i>Copy
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadResult()">
                                    <i class="fas fa-download me-1"></i>Download
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="resultsContainer">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-file-upload fa-3x mb-3"></i>
                                    <p>Upload and process a document to see results here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Display -->
                    <div class="alert alert-danger mt-3" id="errorAlert" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                </div>
            </div>

            <!-- Statistics Row -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                Processing Statistics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center" id="statisticsRow">
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="pageCount">-</div>
                                        <div class="stat-label">Pages</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="tableCount">-</div>
                                        <div class="stat-label">Tables</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="figureCount">-</div>
                                        <div class="stat-label">Figures</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="textItemCount">-</div>
                                        <div class="stat-label">Text Items</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                <strong>Bidbees Web UI</strong> - Powered by Docling SDK for Enterprise Document Processing
                <span class="ms-3">
                    <i class="fas fa-server me-1"></i>
                    <span id="healthStatus">Checking...</span>
                </span>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/script.js"></script>
</body>
</html>
