<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Docling AI - Next-Gen Document Processing</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="/static/modern-style.css" rel="stylesheet">
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="https://www.dropbox.com/static/api/2/dropins.js" id="dropboxjs" data-app-key="YOUR_DROPBOX_APP_KEY"></script>
</head>
<body>
    <!-- Background -->
    <div class="bg-gradient"></div>
    <div class="bg-grid"></div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <div class="brand-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <div class="brand-text">
                    <h1>Docling AI</h1>
                    <span>Next-Gen Document Processing</span>
                </div>
            </div>
            <div class="nav-actions">
                <button class="btn-ghost" onclick="checkHealth()">
                    <i class="fas fa-heartbeat"></i>
                    <span id="healthStatus">Checking...</span>
                </button>
                <button class="btn-ghost" onclick="toggleTheme()">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Hero Section -->
            <div class="hero-section">
                <div class="hero-content">
                    <h1 class="hero-title">
                        Transform Documents with
                        <span class="gradient-text">AI Precision</span>
                    </h1>
                    <p class="hero-subtitle">
                        Advanced document processing powered by cutting-edge AI.
                        Extract, analyze, and convert any document format with unprecedented accuracy.
                    </p>
                </div>
            </div>

            <!-- Processing Panel -->
            <div class="processing-panel">
                <div class="panel-grid">
                    <!-- Upload Section -->
                    <div class="upload-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h2>Upload Document</h2>
                            <p>Drag & drop or click to select your document</p>
                        </div>

                        <form id="uploadForm" enctype="multipart/form-data">
                            <!-- Modern File Upload -->
                            <div class="file-upload-area" id="fileUploadArea">
                                <input type="file" id="fileInput" name="file"
                                       accept=".pdf,.docx,.pptx,.xlsx,.html,.md,.csv,.png,.jpg,.jpeg" required>
                                <div class="upload-content">
                                    <div class="upload-icon">
                                        <i class="fas fa-file-upload"></i>
                                    </div>
                                    <div class="upload-text">
                                        <h3>Drop your document here</h3>
                                        <p>or <span class="upload-link">browse files</span></p>
                                    </div>
                                    <div class="upload-formats">
                                        <span class="format-tag">PDF</span>
                                        <span class="format-tag">DOCX</span>
                                        <span class="format-tag">XLSX</span>
                                        <span class="format-tag">+More</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Processing Options -->
                            <div class="options-grid">
                                <div class="option-card">
                                    <div class="option-header">
                                        <i class="fas fa-eye"></i>
                                        <span>OCR Processing</span>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="enableOcr" name="enable_ocr">
                                        <label for="enableOcr" class="toggle-label">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p class="option-desc">Extract text from scanned documents and images</p>
                                </div>

                                <div class="option-card">
                                    <div class="option-header">
                                        <i class="fas fa-table"></i>
                                        <span>Table Detection</span>
                                    </div>
                                    <div class="toggle-switch">
                                        <input type="checkbox" id="enableTableStructure" name="enable_table_structure" checked>
                                        <label for="enableTableStructure" class="toggle-label">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                    <p class="option-desc">Identify and extract table structures</p>
                                </div>

                                <div class="option-card full-width">
                                    <div class="option-header">
                                        <i class="fas fa-file-export"></i>
                                        <span>Output Format</span>
                                    </div>
                                    <div class="format-selector">
                                        <input type="radio" id="formatMarkdown" name="output_format" value="markdown" checked>
                                        <label for="formatMarkdown" class="format-option">
                                            <i class="fab fa-markdown"></i>
                                            <span>Markdown</span>
                                        </label>

                                        <input type="radio" id="formatHtml" name="output_format" value="html">
                                        <label for="formatHtml" class="format-option">
                                            <i class="fab fa-html5"></i>
                                            <span>HTML</span>
                                        </label>

                                        <input type="radio" id="formatJson" name="output_format" value="json">
                                        <label for="formatJson" class="format-option">
                                            <i class="fas fa-code"></i>
                                            <span>JSON</span>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="action-buttons">
                                <button type="submit" class="btn-primary" id="processBtn">
                                    <i class="fas fa-magic"></i>
                                    <span>Process Document</span>
                                    <div class="btn-glow"></div>
                                </button>

                                <button type="button" class="btn-secondary" id="cancelBtn" disabled>
                                    <i class="fas fa-times"></i>
                                    <span>Cancel</span>
                                </button>
                            </div>
                        </form>

                        <!-- Progress Section -->
                        <div class="progress-section" id="progressContainer">
                            <div class="progress-header">
                                <div class="progress-icon">
                                    <i class="fas fa-cog fa-spin"></i>
                                </div>
                                <div class="progress-text">
                                    <h3 id="progressTitle">Processing Document</h3>
                                    <p id="progressSubtitle">Analyzing document structure...</p>
                                </div>
                                <div class="progress-time">
                                    <span id="processingTime">00:00</span>
                                </div>
                            </div>

                            <div class="progress-bar-container">
                                <div class="progress-bar-modern">
                                    <div class="progress-fill" id="progressFill"></div>
                                </div>
                                <div class="progress-percentage" id="progressPercentage">0%</div>
                            </div>

                            <div class="progress-steps">
                                <div class="step active" id="step1">
                                    <div class="step-icon"><i class="fas fa-upload"></i></div>
                                    <span>Upload</span>
                                </div>
                                <div class="step" id="step2">
                                    <div class="step-icon"><i class="fas fa-search"></i></div>
                                    <span>Analyze</span>
                                </div>
                                <div class="step" id="step3">
                                    <div class="step-icon"><i class="fas fa-magic"></i></div>
                                    <span>Process</span>
                                </div>
                                <div class="step" id="step4">
                                    <div class="step-icon"><i class="fas fa-check"></i></div>
                                    <span>Complete</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Results Section -->
                    <div class="results-section">
                        <div class="section-header">
                            <div class="section-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <h2>Processing Results</h2>
                            <p>Document analysis and extracted content</p>
                        </div>

                        <!-- Document Stats -->
                        <div class="stats-grid" id="documentStats">
                            <!-- Stats will be populated by JavaScript -->
                        </div>

                <!-- Results Section -->
                <div class="col-lg-6">
                    <div class="card shadow-sm">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-file-text me-2"></i>
                                Processing Results
                            </h5>
                            <div class="btn-group" role="group" id="resultActions" style="display: none;">
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="copyToClipboard()">
                                    <i class="fas fa-copy me-1"></i>Copy
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-success" onclick="downloadResult()">
                                    <i class="fas fa-download me-1"></i>Download
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div id="resultsContainer">
                                <div class="text-center text-muted py-5">
                                    <i class="fas fa-file-upload fa-3x mb-3"></i>
                                    <p>Upload and process a document to see results here</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Error Display -->
                    <div class="alert alert-danger mt-3" id="errorAlert" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                </div>
            </div>

            <!-- Statistics Row -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h6 class="mb-0">
                                <i class="fas fa-chart-bar me-2"></i>
                                Processing Statistics
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center" id="statisticsRow">
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="pageCount">-</div>
                                        <div class="stat-label">Pages</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="tableCount">-</div>
                                        <div class="stat-label">Tables</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="figureCount">-</div>
                                        <div class="stat-label">Figures</div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="stat-item">
                                        <div class="stat-number" id="textItemCount">-</div>
                                        <div class="stat-label">Text Items</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-3 mt-5">
        <div class="container">
            <p class="mb-0">
                <strong>Bidbees Web UI</strong> - Powered by Docling SDK for Enterprise Document Processing
                <span class="ms-3">
                    <i class="fas fa-server me-1"></i>
                    <span id="healthStatus">Checking...</span>
                </span>
            </p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/modern-script.js"></script>

    <!-- Success Notification Styles -->
    <style>
        .success-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--gradient-success);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            box-shadow: var(--shadow-xl);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: white;
            margin-bottom: 0.25rem;
        }

        .stat-label {
            font-size: 0.875rem;
            color: var(--gray-400);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content-header h3 {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
        }

        .content-body {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            max-height: 400px;
            overflow-y: auto;
        }

        .content-body pre {
            margin: 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            color: var(--gray-200);
        }

        .empty-state {
            text-align: center;
            padding: 3rem;
            color: var(--gray-400);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .save-btn {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-lg);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            color: white;
            text-decoration: none;
        }

        .save-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .save-icon {
            width: 40px;
            height: 40px;
            background: var(--gradient-primary);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .save-text h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1rem;
            font-weight: 600;
        }

        .save-text p {
            margin: 0;
            font-size: 0.875rem;
            color: var(--gray-400);
        }

        .save-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
    </style>
</body>
</html>
