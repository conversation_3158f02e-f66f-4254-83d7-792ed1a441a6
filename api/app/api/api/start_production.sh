#!/bin/bash

echo "Starting Docling API in production mode..."

# Activate virtual environment
echo "Activating virtual environment..."
source /Users/<USER>/Documents/GitHub/docling/venv/bin/activate

# Set environment variables
echo "Setting environment variables..."
export ENVIRONMENT=production
export LOG_LEVEL=debug
export MAX_WORKERS=4
export PYTHONWARNINGS="ignore::RuntimeWarning"
export PYTHONUNBUFFERED=1

# Create logs directory
echo "Creating logs directory..."
mkdir -p api/logs

# Kill any existing process on port 8080
echo "Checking for existing processes on port 8080..."
lsof -i :8080 | grep LISTEN | awk '{print $2}' | xargs kill -9 2>/dev/null || true

# Start the server
echo "Starting server..."
cd /Users/<USER>/Documents/GitHub/docling
PYTHONPATH=/Users/<USER>/Documents/GitHub/docling gunicorn -c api/gunicorn_config.py app.main:app

echo "Server started. Check api/logs/error.log and api/logs/access.log for details."
