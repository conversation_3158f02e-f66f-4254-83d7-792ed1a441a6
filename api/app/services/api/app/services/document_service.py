import tempfile
from pathlib import Path
from fastapi import HTTP<PERSON>x<PERSON>
from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions

async def process_document(file: UploadFile):
    if not file.filename.lower().endswith(('.pdf', '.docx', '.pptx')):
        raise HTTPException(status_code=400, detail="Unsupported file format")

    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file.flush()
            tmp_file_path = tmp_file.name

            pipeline_options = PdfPipelineOptions()
            pipeline_options.do_ocr = False

            converter = DocumentConverter(
                format_options={
                    InputFormat.PDF: pipeline_options,
                }
            )

            result = converter.convert(tmp_file_path)
            doc = result.document
            
            response_data = {
                "filename": file.filename,
                "content_type": file.content_type,
                "page_count": len(doc.pages) if hasattr(doc, 'pages') else 1,
                "text_content": doc.export_to_markdown(),
                "document_structure": {
                    "title": getattr(doc, 'title', 'No title'),
                    "tables_count": len([item for item in doc.items if item.item_type == 'table']) if hasattr(doc, 'items') else 0,
                    "figures_count": len([item for item in doc.items if item.item_type == 'figure']) if hasattr(doc, 'items') else 0,
                }
            }
            
            return response_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
