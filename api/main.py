import tempfile
import j<PERSON>
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from docling.document_converter import DocumentConverter
import os
from pathlib import Path
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions, AcceleratorDevice
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.backend.docling_parse_v4_backend import DoclingParseV4DocumentBackend
from typing import Optional
import asyncio
import time

# 1. Initialize your application and the converter
# These are created once when the application starts, which is efficient.
app = FastAPI(
    title="Bidbees Web UI",
    description="A powerful web interface for processing large documents using the Docling SDK.",
    version="1.0.0"
)

# Create directories for static files and templates
static_dir = Path("api/static")
templates_dir = Path("api/templates")
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

# Mount static files and setup templates
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
templates = Jinja2Templates(directory=str(templates_dir))

# Instantiate the converter. This will load any necessary models into memory.
converter = DocumentConverter()

# Main UI route
@app.get("/", response_class=HTMLResponse)
async def main_ui(request: Request):
    """Serve the main web UI"""
    return templates.TemplateResponse("index.html", {"request": request})

# 2. Define the API endpoint for processing documents
@app.post("/process-document/")
async def process_document(
    file: UploadFile = File(...),
    enable_ocr: bool = Form(False),
    enable_table_structure: bool = Form(True),
    output_format: str = Form("markdown")
):
    """
    Accepts a document file, processes it with Docling,
    and returns the extracted document structure as JSON.
    """
    # Check supported file formats
    supported_extensions = ('.pdf', '.docx', '.pptx', '.xlsx', '.html', '.md', '.csv', '.png', '.jpg', '.jpeg')
    if not file.filename.lower().endswith(supported_extensions):
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported formats: {', '.join(supported_extensions)}"
        )

    # Use a temporary file to save the upload, as Docling works with file paths
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            tmp_file.write(content)
            tmp_file.flush() # Ensure all content is written to disk
            tmp_file_path = tmp_file.name

            # Use simpler configuration for better reliability
            print(f"Processing file: {file.filename}")
            print(f"OCR enabled: {enable_ocr}")
            print(f"Table structure enabled: {enable_table_structure}")
            print(f"Output format: {output_format}")

            # For large PDFs with OCR, use simpler processing to avoid hangs
            file_size_mb = os.path.getsize(tmp_file_path) / (1024 * 1024)
            if file_size_mb > 2 and enable_ocr and file.filename.lower().endswith('.pdf'):
                print(f"⚠️  Large PDF ({file_size_mb:.1f}MB) with OCR - using simplified processing")
                # Disable OCR for large PDFs to prevent hanging
                enable_ocr = False
                print("   OCR disabled for large PDF to prevent timeout")

            # Use the global converter for simpler processing
            converter_to_use = converter

            # Process the document from its temporary path
            print(f"Processing temporary file: {tmp_file_path}")
            print(f"File size: {os.path.getsize(tmp_file_path)} bytes")

            # Process with timeout based on file size
            start_time = time.time()
            try:
                result = converter_to_use.convert(tmp_file_path)
                processing_time = time.time() - start_time
                print(f"Processing completed in {processing_time:.2f} seconds")
            except Exception as conv_error:
                processing_time = time.time() - start_time
                print(f"Conversion failed after {processing_time:.2f} seconds: {conv_error}")
                raise conv_error

            # Extract content
            doc = result.document

            # Generate content in requested format
            if output_format == "markdown":
                content = doc.export_to_markdown()
            elif output_format == "html":
                content = doc.export_to_html()
            elif output_format == "json":
                content = doc.export_to_json()
            else:
                content = doc.export_to_markdown()  # Default to markdown

            # Count different element types
            tables_count = len([item for item in doc.tables]) if hasattr(doc, 'tables') else 0
            figures_count = len([item for item in doc.pictures]) if hasattr(doc, 'pictures') else 0
            text_items_count = len([item for item in doc.texts]) if hasattr(doc, 'texts') else 0

            # Return processed content
            response_data = {
                "success": True,
                "filename": file.filename,
                "content_type": file.content_type,
                "file_size": len(content),
                "page_count": len(doc.pages) if hasattr(doc, 'pages') else 1,
                "output_format": output_format,
                "content": content,
                "document_structure": {
                    "title": getattr(doc, 'name', file.filename),
                    "tables_count": tables_count,
                    "figures_count": figures_count,
                    "text_items_count": text_items_count,
                },
                "processing_info": {
                    "ocr_enabled": enable_ocr,
                    "table_structure_enabled": enable_table_structure,
                    "conversion_status": str(result.status),
                    "processing_time": f"{processing_time:.2f} seconds",
                    "optimized_for_large_docs": True,
                    "max_pages_supported": 500,
                    "backend_used": "DoclingParseV4"
                }
            }
            
            # Cleanup
            os.unlink(tmp_file_path)
            
            return JSONResponse(content=response_data)

    except Exception as e:
        # Cleanup on error
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass

        # If anything goes wrong, return a detailed error
        print(f"Error processing file: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "filename": file.filename,
                "message": "Failed to process document"
            }
        )

# Add a simple root endpoint for health checks
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "docling_available": True,
        "supported_formats": [".pdf", ".docx", ".pptx", ".xlsx", ".html", ".md", ".csv", ".png", ".jpg", ".jpeg"]
    }

# Get supported formats endpoint
@app.get("/api/formats")
async def get_supported_formats():
    """Get list of supported input and output formats"""
    return {
        "input_formats": [".pdf", ".docx", ".pptx", ".xlsx", ".html", ".md", ".csv", ".png", ".jpg", ".jpeg"],
        "output_formats": ["markdown", "html", "json"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8081)