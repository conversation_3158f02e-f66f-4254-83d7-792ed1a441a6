#!/bin/bash

echo "Starting Docling API in production mode..."

# Activate virtual environment
echo "Activating virtual environment..."
source /Users/<USER>/Documents/GitHub/docling/venv/bin/activate

# Set environment variables
echo "Setting environment variables..."
export ENVIRONMENT=production
export LOG_LEVEL=debug
export MAX_WORKERS=4
export PYTHONWARNINGS="ignore::RuntimeWarning"
export PYTHONUNBUFFERED=1

# Create logs directory if it doesn't exist
echo "Creating logs directory..."
mkdir -p logs

# Kill any existing process on port 8081
echo "Checking for existing processes on port 8081..."
# Change this line
lsof -i :8080 | grep LISTEN | awk '{print $2}' | xargs kill -9 2>/dev/null || true
# To
lsof -i :8081 | grep LISTEN | awk '{print $2}' | xargs kill -9 2>/dev/null || true

# Start the server using the config file
echo "Starting server..."
cd /Users/<USER>/Documents/GitHub/docling
gunicorn -c api/gunicorn_config.py --reload
