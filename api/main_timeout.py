import tempfile
import json
from fastapi import FastAP<PERSON>, File, UploadFile, HTTPException, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from docling.document_converter import DocumentConverter
import os
import time
import concurrent.futures
from pathlib import Path
from typing import Optional

# Initialize FastAPI app
app = FastAPI(
    title="Bidbees Web UI - Timeout Fixed",
    description="Document processing with proper timeout handling for problematic PDFs",
    version="1.1.0"
)

# Create directories for static files and templates
static_dir = Path("api/static")
templates_dir = Path("api/templates")
static_dir.mkdir(exist_ok=True)
templates_dir.mkdir(exist_ok=True)

# Mount static files and setup templates
app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
templates = Jinja2Templates(directory=str(templates_dir))

# Initialize converter with error handling
try:
    converter = DocumentConverter()
    print("✅ DocumentConverter initialized successfully")
except Exception as e:
    print(f"❌ Failed to initialize DocumentConverter: {e}")
    converter = None

# Main UI route
@app.get("/", response_class=HTMLResponse)
async def main_ui(request: Request):
    """Serve the main web UI"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/process-document/")
async def process_document(
    file: UploadFile = File(...),
    enable_ocr: bool = Form(False),
    enable_table_structure: bool = Form(True),
    output_format: str = Form("markdown")
):
    """
    Process document with proper timeout handling for problematic PDFs
    """
    # Check if converter is available
    if converter is None:
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": "DocumentConverter not initialized",
                "message": "Server initialization failed"
            }
        )
    
    # Check supported file formats
    supported_extensions = ('.pdf', '.docx', '.pptx', '.xlsx', '.html', '.md', '.csv', '.png', '.jpg', '.jpeg')
    if not file.filename.lower().endswith(supported_extensions):
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file format. Supported formats: {', '.join(supported_extensions)}"
        )

    # Use a temporary file to save the upload
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=Path(file.filename).suffix) as tmp_file:
            # Write the uploaded file content to the temporary file
            content = await file.read()
            tmp_file.write(content)
            tmp_file.flush()
            tmp_file_path = tmp_file.name

            # Get file info
            file_size_mb = os.path.getsize(tmp_file_path) / (1024 * 1024)
            print(f"Processing file: {file.filename}")
            print(f"File size: {file_size_mb:.2f} MB")
            print(f"OCR enabled: {enable_ocr}")
            print(f"Table structure enabled: {enable_table_structure}")
            print(f"Output format: {output_format}")

            # Special handling for problematic files
            is_problematic_file = (
                "NWDOH" in file.filename.upper() or 
                "ADVERT" in file.filename.upper() or
                file_size_mb > 2.5
            )
            
            if is_problematic_file:
                print(f"⚠️  Detected potentially problematic file: {file.filename}")
                # Disable OCR for problematic files to prevent hanging
                if enable_ocr:
                    print("   Disabling OCR to prevent timeout")
                    enable_ocr = False

            # Set timeout based on file characteristics
            if is_problematic_file:
                timeout_seconds = 90  # 1.5 minutes for problematic files
            elif file_size_mb > 2:
                timeout_seconds = 120  # 2 minutes for large files
            else:
                timeout_seconds = 60   # 1 minute for normal files

            print(f"Setting timeout to {timeout_seconds} seconds")

            # Define conversion function
            def convert_document():
                try:
                    return converter.convert(tmp_file_path)
                except Exception as e:
                    print(f"Conversion error: {e}")
                    raise e

            # Process with timeout
            start_time = time.time()
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(convert_document)
                    try:
                        result = future.result(timeout=timeout_seconds)
                        processing_time = time.time() - start_time
                        print(f"Processing completed in {processing_time:.2f} seconds")
                    except concurrent.futures.TimeoutError:
                        processing_time = time.time() - start_time
                        print(f"Processing timed out after {processing_time:.2f} seconds")
                        
                        # Try to cancel the future
                        future.cancel()
                        
                        error_msg = (
                            f"Document processing timed out after {timeout_seconds} seconds. "
                            f"This PDF ('{file.filename}') appears to have complex formatting that "
                            f"requires extensive processing time. Suggestions: "
                            f"1) Try with OCR disabled, 2) Use a simpler PDF, or 3) Contact support."
                        )
                        raise Exception(error_msg)

            except Exception as conv_error:
                processing_time = time.time() - start_time
                print(f"Conversion failed after {processing_time:.2f} seconds: {conv_error}")
                raise conv_error

            # Extract content
            doc = result.document

            # Generate content in requested format
            if output_format == "markdown":
                content = doc.export_to_markdown()
            elif output_format == "html":
                content = doc.export_to_html()
            elif output_format == "json":
                content = doc.export_to_json()
            else:
                content = doc.export_to_markdown()

            # Count different element types
            tables_count = len([item for item in doc.tables]) if hasattr(doc, 'tables') else 0
            figures_count = len([item for item in doc.pictures]) if hasattr(doc, 'pictures') else 0
            text_items_count = len([item for item in doc.texts]) if hasattr(doc, 'texts') else 0

            # Return processed content
            response_data = {
                "success": True,
                "filename": file.filename,
                "content_type": file.content_type,
                "file_size": len(content),
                "page_count": len(doc.pages) if hasattr(doc, 'pages') else 1,
                "output_format": output_format,
                "content": content,
                "document_structure": {
                    "title": getattr(doc, 'name', file.filename),
                    "tables_count": tables_count,
                    "figures_count": figures_count,
                    "text_items_count": text_items_count,
                },
                "processing_info": {
                    "ocr_enabled": enable_ocr,
                    "table_structure_enabled": enable_table_structure,
                    "conversion_status": str(result.status),
                    "processing_time": f"{processing_time:.2f} seconds",
                    "timeout_used": f"{timeout_seconds} seconds",
                    "was_problematic_file": is_problematic_file,
                    "backend_used": "DocumentConverter"
                }
            }
            
            # Cleanup
            os.unlink(tmp_file_path)
            
            return JSONResponse(content=response_data)

    except Exception as e:
        # Cleanup on error
        if 'tmp_file_path' in locals():
            try:
                os.unlink(tmp_file_path)
            except:
                pass

        # Return detailed error
        print(f"Error processing file: {e}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": str(e),
                "filename": file.filename,
                "message": "Failed to process document"
            }
        )

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy" if converter is not None else "unhealthy",
        "docling_available": converter is not None,
        "converter_initialized": converter is not None,
        "version": "1.1.0-timeout-fixed",
        "supported_formats": [".pdf", ".docx", ".pptx", ".xlsx", ".html", ".md", ".csv", ".png", ".jpg", ".jpeg"]
    }

@app.get("/api/formats")
async def get_supported_formats():
    """Get list of supported input and output formats"""
    return {
        "input_formats": [".pdf", ".docx", ".pptx", ".xlsx", ".html", ".md", ".csv", ".png", ".jpg", ".jpeg"],
        "output_formats": ["markdown", "html", "json"]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8082)
