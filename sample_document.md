# Sample Document for Testing

This is a sample document to test the Docling Web UI functionality.

## Introduction

Docling is a powerful document processing system that can handle various document formats including:

- PDF files
- Microsoft Word documents (DOCX)
- PowerPoint presentations (PPTX)
- Excel spreadsheets (XLSX)
- HTML files
- Markdown files
- CSV files
- Images (PNG, JPG, JPEG)

## Features

### Document Processing
The system provides advanced document processing capabilities:

1. **Text Extraction**: Extract text content from documents
2. **Layout Analysis**: Understand document structure and layout
3. **Table Detection**: Identify and extract table structures
4. **OCR Support**: Optical Character Recognition for scanned documents
5. **Multi-format Output**: Export to Markdown, HTML, or JSON

### Web Interface
The web interface offers:

- Drag and drop file upload
- Real-time processing progress
- Interactive results display
- Copy and download functionality
- Document statistics

## Example Table

| Feature | Description | Status |
|---------|-------------|--------|
| PDF Processing | Advanced PDF understanding | ✅ Available |
| OCR | Optical Character Recognition | ✅ Available |
| Table Detection | Automatic table structure detection | ✅ Available |
| Multi-format Export | Markdown, HTML, JSON output | ✅ Available |

## Code Example

```python
from docling.document_converter import DocumentConverter

# Initialize the converter
converter = DocumentConverter()

# Convert a document
result = converter.convert("document.pdf")

# Export to markdown
markdown_content = result.document.export_to_markdown()
print(markdown_content)
```

## Conclusion

This sample document demonstrates various elements that Docling can process:
- Headers and subheaders
- Lists (both ordered and unordered)
- Tables
- Code blocks
- Text formatting

The system should be able to parse and extract all these elements correctly.
