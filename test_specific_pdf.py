#!/usr/bin/env python3
"""
Test specific PDF file parsing
"""

import requests
import json
import time
from pathlib import Path

def test_pdf_file(pdf_path, enable_ocr=False):
    """Test parsing of a specific PDF file"""
    try:
        pdf_file = Path(pdf_path)
        if not pdf_file.exists():
            print(f"❌ PDF file not found: {pdf_path}")
            return False
            
        file_size_mb = pdf_file.stat().st_size / (1024 * 1024)
        print(f"🔄 Testing PDF: {pdf_file.name}")
        print(f"📄 File size: {file_size_mb:.2f} MB")
        print(f"👁️  OCR enabled: {enable_ocr}")
        
        with open(pdf_file, 'rb') as f:
            files = {'file': (pdf_file.name, f, 'application/pdf')}
            data = {
                'enable_ocr': str(enable_ocr).lower(),
                'enable_table_structure': 'true',
                'output_format': 'markdown'
            }
            
            print("⏳ Sending request...")
            start_time = time.time()
            
            response = requests.post(
                "http://localhost:8081/process-document/",
                files=files,
                data=data,
                timeout=120
            )
            
            processing_time = time.time() - start_time
            print(f"⏱️  Total time: {processing_time:.2f} seconds")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                print("✅ PDF parsing successful!")
                print(f"   Filename: {result.get('filename')}")
                print(f"   Pages: {result.get('page_count', 0)}")
                print(f"   Content length: {len(result.get('content', ''))}")
                
                # Document structure
                if 'document_structure' in result:
                    struct = result['document_structure']
                    print(f"   Tables: {struct.get('tables_count', 0)}")
                    print(f"   Figures: {struct.get('figures_count', 0)}")
                    print(f"   Text items: {struct.get('text_items_count', 0)}")
                
                # Processing info
                if 'processing_info' in result:
                    proc_info = result['processing_info']
                    print(f"   Processing time: {proc_info.get('processing_time')}")
                    print(f"   OCR used: {proc_info.get('ocr_enabled')}")
                    print(f"   Status: {proc_info.get('conversion_status')}")
                
                # Show content sample
                content = result.get('content', '')
                if content:
                    print(f"\n📝 First 400 characters:")
                    print("-" * 50)
                    print(content[:400])
                    if len(content) > 400:
                        print("...")
                    print("-" * 50)
                    
                    # Save to file
                    output_file = f"parsed_{pdf_file.stem}_ocr_{enable_ocr}.md"
                    with open(output_file, 'w', encoding='utf-8') as out_f:
                        out_f.write(content)
                    print(f"💾 Saved to: {output_file}")
                
                return True
            else:
                print(f"❌ Failed with status: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"   Error: {error_info.get('error')}")
                except:
                    print(f"   Response: {response.text[:200]}")
                return False
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Test PDF parsing with test files"""
    print("🧪 Testing PDF Parsing with Test Files")
    print("=" * 50)
    
    # Test files to try
    test_pdfs = [
        "tests/data/pdf/2206.01062.pdf",  # Small research paper
        "tests/data/pdf/multi_page.pdf",  # Multi-page document
        "tests/data/pdf/2305.03393v1-pg9.pdf",  # Single page
    ]
    
    for pdf_path in test_pdfs:
        if Path(pdf_path).exists():
            print(f"\n--- Testing {Path(pdf_path).name} ---")
            
            # Test without OCR first
            print("\n🔸 Without OCR:")
            success_no_ocr = test_pdf_file(pdf_path, enable_ocr=False)
            
            # Test with OCR if file is small
            file_size_mb = Path(pdf_path).stat().st_size / (1024 * 1024)
            if file_size_mb < 3:  # Only test OCR on small files
                print("\n🔸 With OCR:")
                success_with_ocr = test_pdf_file(pdf_path, enable_ocr=True)
            else:
                print(f"\n🔸 Skipping OCR (file too large: {file_size_mb:.1f}MB)")
                success_with_ocr = None
            
            print(f"\n📊 Results:")
            print(f"   No OCR: {'✅' if success_no_ocr else '❌'}")
            if success_with_ocr is not None:
                print(f"   With OCR: {'✅' if success_with_ocr else '❌'}")
        else:
            print(f"❌ File not found: {pdf_path}")
    
    print("\n" + "=" * 50)
    print("🏁 PDF testing completed!")

if __name__ == "__main__":
    main()
