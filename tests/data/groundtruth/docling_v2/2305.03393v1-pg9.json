{"schema_name": "DoclingDocument", "version": "1.3.0", "name": "2305.03393v1-pg9", "origin": {"mimetype": "application/pdf", "binary_hash": 3463920545297462180, "filename": "2305.03393v1-pg9.pdf", "uri": null}, "furniture": {"self_ref": "#/furniture", "parent": null, "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "parent": null, "children": [{"cref": "#/texts/0"}, {"cref": "#/texts/1"}, {"cref": "#/texts/2"}, {"cref": "#/texts/3"}, {"cref": "#/texts/4"}, {"cref": "#/tables/0"}, {"cref": "#/texts/6"}, {"cref": "#/texts/7"}, {"cref": "#/texts/8"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [], "texts": [{"self_ref": "#/texts/0", "parent": {"cref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 194.478, "t": 700.50647, "r": 447.54476999999997, "b": 689.21777, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 60]}], "orig": "Optimized Table Tokenization for Table Structure Recognition", "text": "Optimized Table Tokenization for Table Structure Recognition", "formatting": null, "hyperlink": null}, {"self_ref": "#/texts/1", "parent": {"cref": "#/body"}, "children": [], "content_layer": "furniture", "label": "page_header", "prov": [{"page_no": 1, "bbox": {"l": 475.98441, "t": 700.50647, "r": 480.59314, "b": 689.21777, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 1]}], "orig": "9", "text": "9", "formatting": null, "hyperlink": null}, {"self_ref": "#/texts/2", "parent": {"cref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 675.53699, "r": 480.59665, "b": 639.09302, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 163]}], "orig": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz.", "text": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz.", "formatting": null, "hyperlink": null}, {"self_ref": "#/texts/3", "parent": {"cref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 625.29486, "r": 318.45145, "b": 612.79181, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "5.1 Hyper Parameter Optimization", "text": "5.1 Hyper Parameter Optimization", "formatting": null, "hyperlink": null, "level": 1}, {"self_ref": "#/texts/4", "parent": {"cref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 608.88495, "r": 480.59567, "b": 536.57599, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 423]}], "orig": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML.", "text": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML.", "formatting": null, "hyperlink": null}, {"self_ref": "#/texts/5", "parent": {"cref": "#/tables/0"}, "children": [], "content_layer": "body", "label": "caption", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 519.20526, "r": 480.59890999999993, "b": 464.01782, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 398]}], "orig": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "formatting": null, "hyperlink": null}, {"self_ref": "#/texts/6", "parent": {"cref": "#/body"}, "children": [], "content_layer": "body", "label": "section_header", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 286.32889, "r": 264.40829, "b": 273.82581000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "5.2 Quantitative Results", "text": "5.2 Quantitative Results", "formatting": null, "hyperlink": null, "level": 1}, {"self_ref": "#/texts/7", "parent": {"cref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 269.91995, "r": 480.72003, "b": 173.70000000000005, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 555]}], "orig": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on difficult financial tables (FinTabNet) that contain sparse and large tables.", "text": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on difficult financial tables (FinTabNet) that contain sparse and large tables.", "formatting": null, "hyperlink": null}, {"self_ref": "#/texts/8", "parent": {"cref": "#/body"}, "children": [], "content_layer": "body", "label": "text", "prov": [{"page_no": 1, "bbox": {"l": 134.765, "t": 174.27795000000003, "r": 480.59857000000005, "b": 125.88, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 289]}], "orig": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation.", "text": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation.", "formatting": null, "hyperlink": null}], "pictures": [], "tables": [{"self_ref": "#/tables/0", "parent": {"cref": "#/body"}, "children": [{"cref": "#/texts/5"}], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 139.66746520996094, "t": 454.4546203613281, "r": 475.0093078613281, "b": 322.5054626464844, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [{"cref": "#/texts/5"}], "references": [], "footnotes": [], "image": null, "data": {"table_cells": [{"bbox": {"l": 160.37, "t": 339.45749, "r": 168.04523, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.974, "t": 339.45749, "r": 215.64923000000002, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.79799999999997, "t": 344.93649, "r": 278.3338, "b": 356.22519000000005, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67001, "t": 339.45749, "r": 348.26419, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 396.271, "t": 339.45749, "r": 417.12595, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "mAP", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 394.927, "t": 350.41647, "r": 418.46921, "b": 361.70517, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 430.771, "t": 339.45749, "r": 467.14142000000004, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "Inference", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 427.14801, "t": 350.41647, "r": 470.76955999999996, "b": 361.70517, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "time (secs)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 286.686, "t": 352.40848, "r": 312.32812, "b": 363.69717, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 320.702, "t": 352.40848, "r": 353.71539, "b": 363.69717, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 369.306, "t": 352.40848, "r": 379.02914, "b": 363.69717, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 161.90601, "t": 371.23849, "r": 166.51474, "b": 382.52719, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 371.23849, "r": 214.11774, "b": 382.52719, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 365.75848, "r": 271.41064, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 365.75848, "r": 310.00732, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.71701, "t": 365.75848, "r": 347.70734, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.67599, "t": 365.75848, "r": 384.66632, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 397.26999, "t": 365.69571, "r": 416.12634, "b": 377.10098000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 365.69571, "r": 458.38336, "b": 377.10098000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.90601, "t": 397.53949, "r": 166.51474, "b": 408.82819, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 397.53949, "r": 214.11774, "b": 408.82819, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 392.06049, "r": 271.41064, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 392.06049, "r": 310.00732, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.938 0.952", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.71701, "t": 392.06049, "r": 347.70734, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.904 0.909", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.67599, "t": 392.06049, "r": 384.66632, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.927 0.938", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.61801, "t": 391.99771, "r": 418.77798, "b": 403.40298, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.853 0.843", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 391.99771, "r": 458.38336, "b": 403.40298, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.97 3.77", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.90601, "t": 423.84048, "r": 166.51474, "b": 435.12918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 423.84048, "r": 214.11774, "b": 435.12918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 418.3614799999999, "r": 271.41064, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 418.3614799999999, "r": 310.00732, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.923 0.945", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.71701, "t": 418.3614799999999, "r": 347.70734, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.897 0.901", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.67599, "t": 418.3614799999999, "r": 384.66632, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.915 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.61801, "t": 418.29871, "r": 418.77798, "b": 429.70398, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.859 0.834", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 418.29871, "r": 458.38336, "b": 429.70398, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.91 3.81", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 161.90601, "t": 450.14248999999995, "r": 166.51474, "b": 461.43118, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 450.14248999999995, "r": 214.11774, "b": 461.43118, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 444.66248, "r": 271.41064, "b": 455.95117, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 444.66248, "r": 310.00732, "b": 455.95117, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.952 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 329.021, "t": 444.66248, "r": 345.40439, "b": 455.95117, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.92 0.903", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 362.08801, "t": 444.5996999999999, "r": 386.24799, "b": 456.00497, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.942 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.61801, "t": 444.5996999999999, "r": 418.77798, "b": 456.00497, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.857 0.824", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 444.5996999999999, "r": 458.38336, "b": 456.00497, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.22 2", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 6, "num_cols": 8, "grid": [[{"bbox": {"l": 160.37, "t": 339.45749, "r": 168.04523, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.974, "t": 339.45749, "r": 215.64923000000002, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.79799999999997, "t": 344.93649, "r": 278.3338, "b": 356.22519000000005, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67001, "t": 339.45749, "r": 348.26419, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67001, "t": 339.45749, "r": 348.26419, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 324.67001, "t": 339.45749, "r": 348.26419, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 3, "end_col_offset_idx": 6, "text": "TEDs", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 396.271, "t": 339.45749, "r": 417.12595, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "mAP", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 430.771, "t": 339.45749, "r": 467.14142000000004, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "Inference", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 160.37, "t": 339.45749, "r": 168.04523, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "# enc-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 207.974, "t": 339.45749, "r": 215.64923000000002, "b": 350.74619, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "# dec-layers", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 239.79799999999997, "t": 344.93649, "r": 278.3338, "b": 356.22519000000005, "coord_origin": "TOPLEFT"}, "row_span": 2, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 2, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "Language", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 286.686, "t": 352.40848, "r": 312.32812, "b": 363.69717, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "simple", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 320.702, "t": 352.40848, "r": 353.71539, "b": 363.69717, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "complex", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 369.306, "t": 352.40848, "r": 379.02914, "b": 363.69717, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "all", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 394.927, "t": 350.41647, "r": 418.46921, "b": 361.70517, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "(0.75)", "column_header": true, "row_header": false, "row_section": false}, {"bbox": {"l": 427.14801, "t": 350.41647, "r": 470.76955999999996, "b": 361.70517, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "time (secs)", "column_header": true, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.90601, "t": 371.23849, "r": 166.51474, "b": 382.52719, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 371.23849, "r": 214.11774, "b": 382.52719, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "6", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 365.75848, "r": 271.41064, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 365.75848, "r": 310.00732, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.965 0.969", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.71701, "t": 365.75848, "r": 347.70734, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.934 0.927", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.67599, "t": 365.75848, "r": 384.66632, "b": 377.04717999999997, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.955 0.955", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 397.26999, "t": 365.69571, "r": 416.12634, "b": 377.10098000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.88 0.857", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 365.69571, "r": 458.38336, "b": 377.10098000000005, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "2.73 5.39", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.90601, "t": 397.53949, "r": 166.51474, "b": 408.82819, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 397.53949, "r": 214.11774, "b": 408.82819, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 392.06049, "r": 271.41064, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 392.06049, "r": 310.00732, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.938 0.952", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.71701, "t": 392.06049, "r": 347.70734, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.904 0.909", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.67599, "t": 392.06049, "r": 384.66632, "b": 403.34918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.927 0.938", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.61801, "t": 391.99771, "r": 418.77798, "b": 403.40298, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.853 0.843", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 391.99771, "r": 458.38336, "b": 403.40298, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.97 3.77", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.90601, "t": 423.84048, "r": 166.51474, "b": 435.12918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 423.84048, "r": 214.11774, "b": 435.12918, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 418.3614799999999, "r": 271.41064, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 418.3614799999999, "r": 310.00732, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.923 0.945", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 326.71701, "t": 418.3614799999999, "r": 347.70734, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.897 0.901", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 363.67599, "t": 418.3614799999999, "r": 384.66632, "b": 429.65018, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.915 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.61801, "t": 418.29871, "r": 418.77798, "b": 429.70398, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.859 0.834", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 418.29871, "r": 458.38336, "b": 429.70398, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.91 3.81", "column_header": false, "row_header": false, "row_section": false}], [{"bbox": {"l": 161.90601, "t": 450.14248999999995, "r": 166.51474, "b": 461.43118, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "4", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 209.509, "t": 450.14248999999995, "r": 214.11774, "b": 461.43118, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "2", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 246.71000999999998, "t": 444.66248, "r": 271.41064, "b": 455.95117, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "OTSL HTML", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 289.017, "t": 444.66248, "r": 310.00732, "b": 455.95117, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "0.952 0.944", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 329.021, "t": 444.66248, "r": 345.40439, "b": 455.95117, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "0.92 0.903", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 362.08801, "t": 444.5996999999999, "r": 386.24799, "b": 456.00497, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "0.942 0.931", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 394.61801, "t": 444.5996999999999, "r": 418.77798, "b": 456.00497, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "0.857 0.824", "column_header": false, "row_header": false, "row_section": false}, {"bbox": {"l": 439.52701, "t": 444.5996999999999, "r": 458.38336, "b": 456.00497, "coord_origin": "TOPLEFT"}, "row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 7, "end_col_offset_idx": 8, "text": "1.22 2", "column_header": false, "row_header": false, "row_section": false}]]}}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 612.0, "height": 792.0}, "image": null, "page_no": 1}}}