{"schema_name": "DoclingDocument", "version": "1.3.0", "name": "powerpoint_sample", "origin": {"mimetype": "application/vnd.ms-powerpoint", "binary_hash": 15572290240354948364, "filename": "powerpoint_sample.pptx"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/groups/0"}, {"$ref": "#/groups/1"}, {"$ref": "#/groups/2"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/tables/0"}], "content_layer": "body", "name": "slide-0", "label": "chapter"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}], "content_layer": "body", "name": "slide-1", "label": "chapter"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/groups/3"}, {"$ref": "#/groups/4"}, {"$ref": "#/texts/16"}, {"$ref": "#/groups/5"}, {"$ref": "#/texts/19"}, {"$ref": "#/groups/6"}, {"$ref": "#/groups/7"}, {"$ref": "#/texts/26"}], "content_layer": "body", "name": "slide-2", "label": "chapter"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}, {"$ref": "#/texts/11"}], "content_layer": "body", "name": "list", "label": "ordered_list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/texts/17"}, {"$ref": "#/texts/18"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}], "content_layer": "body", "name": "list", "label": "ordered_list"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/groups/2"}, "children": [{"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "title", "prov": [{"page_no": 1, "bbox": {"l": 1524000.0, "t": 1576935.0, "r": 10668000.0, "b": 644526.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 16]}], "orig": "Test Table Slide", "text": "Test Table Slide"}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 1, "bbox": {"l": 1524000.0, "t": 5888420.0, "r": 10668000.0, "b": 5433848.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 13]}], "orig": "With footnote", "text": "With footnote"}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "title", "prov": [{"page_no": 2, "bbox": {"l": 838200.0, "t": 1690688.0, "r": 11353800.0, "b": 365125.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 18]}], "orig": "Second slide title", "text": "Second slide title"}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 2, "bbox": {"l": 838200.0, "t": 3962400.0, "r": 4461831.0, "b": 1825625.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Let’s introduce a list", "text": "Let’s introduce a list"}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 2, "bbox": {"l": 838200.0, "t": 3962400.0, "r": 4461831.0, "b": 1825625.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "With foo", "text": "With foo"}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 2, "bbox": {"l": 838200.0, "t": 3962400.0, "r": 4461831.0, "b": 1825625.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "Bar", "text": "Bar"}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 2, "bbox": {"l": 838200.0, "t": 3962400.0, "r": 4461831.0, "b": 1825625.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 50]}], "orig": "And baz things", "text": "And baz things"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 2, "bbox": {"l": 6180463.0, "t": 5221995.0, "r": 10256704.0, "b": 1344058.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 40]}], "orig": "A rectangle shape with this text inside.", "text": "A rectangle shape with this text inside."}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "furniture", "label": "text", "prov": [{"page_no": 2, "bbox": {"l": 0.0, "t": 0.0, "r": 0.0, "b": 0.0, "coord_origin": "TOPLEFT"}, "charspan": [0, 31]}], "orig": "Some notes on the second slide.", "text": "Some notes on the second slide."}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 2423634.0, "t": 3357995.0, "r": 3928277.0, "b": 2434665.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "List item4", "text": "List item4", "enumerated": true, "marker": "1."}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 2423634.0, "t": 3357995.0, "r": 3928277.0, "b": 2434665.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "List item5", "text": "List item5", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 2423634.0, "t": 3357995.0, "r": 3928277.0, "b": 2434665.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 32]}], "orig": "List item6", "text": "List item6", "enumerated": true, "marker": "3."}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 4453634.0, "t": 3657882.0, "r": 5109583.0, "b": 2457553.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "I1", "text": "I1", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 4453634.0, "t": 3657882.0, "r": 5109583.0, "b": 2457553.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "I2", "text": "I2", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 4453634.0, "t": 3657882.0, "r": 5109583.0, "b": 2457553.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "I3", "text": "I3", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 4453634.0, "t": 3657882.0, "r": 5109583.0, "b": 2457553.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 11]}], "orig": "I4", "text": "I4", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 3, "bbox": {"l": 5634940.0, "t": 3380884.0, "r": 6881050.0, "b": 2457554.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "Some info:", "text": "Some info:"}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 5634940.0, "t": 3380884.0, "r": 6881050.0, "b": 2457554.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "Item A", "text": "Item A", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 5634940.0, "t": 3380884.0, "r": 6881050.0, "b": 2457554.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 24]}], "orig": "<PERSON><PERSON>", "text": "<PERSON><PERSON>", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [{"page_no": 3, "bbox": {"l": 7531336.0, "t": 3659750.0, "r": 9009626.0, "b": 2459421.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "Maybe a list?", "text": "Maybe a list?"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 7531336.0, "t": 3659750.0, "r": 9009626.0, "b": 2459421.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "List1", "text": "List1", "enumerated": true, "marker": "1."}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 7531336.0, "t": 3659750.0, "r": 9009626.0, "b": 2459421.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "List2", "text": "List2", "enumerated": true, "marker": "2."}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 7531336.0, "t": 3659750.0, "r": 9009626.0, "b": 2459421.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 31]}], "orig": "List3", "text": "List3", "enumerated": true, "marker": "3."}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 9404392.0, "t": 3357995.0, "r": 10060341.0, "b": 2434665.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "l1 ", "text": "l1 ", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 9404392.0, "t": 3357995.0, "r": 10060341.0, "b": 2434665.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "l2", "text": "l2", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [{"page_no": 3, "bbox": {"l": 9404392.0, "t": 3357995.0, "r": 10060341.0, "b": 2434665.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 9]}], "orig": "l3", "text": "l3", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "furniture", "label": "text", "prov": [{"page_no": 3, "bbox": {"l": 0.0, "t": 0.0, "r": 0.0, "b": 0.0, "coord_origin": "TOPLEFT"}, "charspan": [0, 53]}], "orig": "Final notes on the third slide.\nSecond line of notes.", "text": "Final notes on the third slide.\nSecond line of notes."}], "pictures": [], "tables": [{"self_ref": "#/tables/0", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "table", "prov": [{"page_no": 1, "bbox": {"l": 2031999.0, "t": 5283200.0, "r": 10160000.0, "b": 1945640.0, "coord_origin": "BOTTOMLEFT"}, "charspan": [0, 0]}], "captions": [], "references": [], "footnotes": [], "data": {"table_cells": [{"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Class1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 7, "text": "Class2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "A merged with B", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "C", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "A", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "B", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "C", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 2, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 3, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R4", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "False", "column_header": false, "row_header": false, "row_section": false}], "num_rows": 9, "num_cols": 7, "grid": [[{"row_span": 1, "col_span": 1, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Class1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Class1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 1, "end_col_offset_idx": 4, "text": "Class1", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 7, "text": "Class2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 7, "text": "Class2", "column_header": true, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 3, "start_row_offset_idx": 0, "end_row_offset_idx": 1, "start_col_offset_idx": 4, "end_col_offset_idx": 7, "text": "Class2", "column_header": true, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "A merged with B", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 2, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 1, "end_col_offset_idx": 3, "text": "A merged with B", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "C", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "A", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "B", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 1, "end_row_offset_idx": 2, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "C", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R1", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 2, "end_row_offset_idx": 3, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "True", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R2", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 3, "end_row_offset_idx": 4, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 2, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 5, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 2, "col_span": 1, "start_row_offset_idx": 4, "end_row_offset_idx": 6, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R3", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 5, "end_row_offset_idx": 6, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 3, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R4", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 7, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 3, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R4", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 7, "end_row_offset_idx": 8, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "False", "column_header": false, "row_header": false, "row_section": false}], [{"row_span": 3, "col_span": 1, "start_row_offset_idx": 6, "end_row_offset_idx": 9, "start_col_offset_idx": 0, "end_col_offset_idx": 1, "text": "R4", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 1, "end_col_offset_idx": 2, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 2, "end_col_offset_idx": 3, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 3, "end_col_offset_idx": 4, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 4, "end_col_offset_idx": 5, "text": "False", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 5, "end_col_offset_idx": 6, "text": "True", "column_header": false, "row_header": false, "row_section": false}, {"row_span": 1, "col_span": 1, "start_row_offset_idx": 8, "end_row_offset_idx": 9, "start_col_offset_idx": 6, "end_col_offset_idx": 7, "text": "False", "column_header": false, "row_header": false, "row_section": false}]]}}], "key_value_items": [], "form_items": [], "pages": {"1": {"size": {"width": 12192000.0, "height": 6858000.0}, "page_no": 1}, "2": {"size": {"width": 12192000.0, "height": 6858000.0}, "page_no": 2}, "3": {"size": {"width": 12192000.0, "height": 6858000.0}, "page_no": 3}}}