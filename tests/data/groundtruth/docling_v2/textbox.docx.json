{"schema_name": "DoclingDocument", "version": "1.3.0", "name": "textbox", "origin": {"mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "binary_hash": 830302052279341882, "filename": "textbox.docx"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/texts/0"}, {"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/groups/0"}, {"$ref": "#/texts/7"}, {"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/groups/2"}, {"$ref": "#/texts/11"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/groups/3"}, {"$ref": "#/texts/16"}, {"$ref": "#/texts/17"}, {"$ref": "#/groups/4"}, {"$ref": "#/texts/22"}, {"$ref": "#/texts/23"}, {"$ref": "#/texts/24"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/26"}, {"$ref": "#/texts/27"}, {"$ref": "#/groups/5"}, {"$ref": "#/groups/7"}, {"$ref": "#/texts/35"}, {"$ref": "#/groups/8"}, {"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/texts/41"}, {"$ref": "#/texts/42"}, {"$ref": "#/texts/43"}, {"$ref": "#/groups/9"}, {"$ref": "#/texts/49"}, {"$ref": "#/texts/50"}, {"$ref": "#/texts/51"}, {"$ref": "#/groups/11"}, {"$ref": "#/texts/55"}, {"$ref": "#/groups/12"}, {"$ref": "#/texts/58"}, {"$ref": "#/texts/59"}, {"$ref": "#/groups/13"}, {"$ref": "#/texts/60"}, {"$ref": "#/groups/14"}, {"$ref": "#/texts/61"}, {"$ref": "#/texts/62"}, {"$ref": "#/groups/15"}, {"$ref": "#/texts/67"}, {"$ref": "#/groups/16"}, {"$ref": "#/texts/68"}, {"$ref": "#/texts/69"}, {"$ref": "#/texts/70"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/groups/1"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/groups/0"}, "children": [{"$ref": "#/texts/6"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/10"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/15"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/28"}, {"$ref": "#/texts/29"}, {"$ref": "#/groups/6"}, {"$ref": "#/texts/32"}, {"$ref": "#/texts/33"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/groups/5"}, "children": [{"$ref": "#/texts/30"}, {"$ref": "#/texts/31"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/34"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/8", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/36"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/9", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/groups/10"}, {"$ref": "#/texts/47"}, {"$ref": "#/texts/48"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/10", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}, {"$ref": "#/texts/46"}], "content_layer": "body", "name": "group", "label": "inline"}, {"self_ref": "#/groups/11", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/52"}, {"$ref": "#/texts/53"}, {"$ref": "#/texts/54"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/12", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/56"}, {"$ref": "#/texts/57"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/13", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/14", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/15", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/63"}, {"$ref": "#/texts/64"}, {"$ref": "#/texts/65"}, {"$ref": "#/texts/66"}], "content_layer": "body", "name": "textbox", "label": "section"}, {"self_ref": "#/groups/16", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "name": "textbox", "label": "section"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Chiayi County Shuishang Township Nanjing Elementary School Affiliated Kindergarten", "text": "Chiayi County Shuishang Township Nanjing Elementary School Affiliated Kindergarten", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Infectious Disease Reporting Procedure for the 113th Academic Year Kindergarten", "text": "Infectious Disease Reporting Procedure for the 113th Academic Year Kindergarten", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Student falls ill", "text": "Student falls ill", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/groups/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/groups/1"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Suggested Reportable Symptoms:\n＊ Fever\n＊ Cough\n＊ Diarrhea\n＊ Vomiting\n＊ Rash\n＊ Blisters\n＊ Headache\n＊ Sore throat", "text": "Suggested Reportable Symptoms:\n＊ Fever\n＊ Cough\n＊ Diarrhea\n＊ Vomiting\n＊ Rash\n＊ Blisters\n＊ Headache\n＊ Sore throat", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "", "text": "", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "If a caregiver suspects that within one week, a fifth of the class (for classes with more than 15 students) or more than three students (for classes with 15 or fewer students)\nshow the same suggested reportable symptoms", "text": "If a caregiver suspects that within one week, a fifth of the class (for classes with more than 15 students) or more than three students (for classes with 15 or fewer students)\nshow the same suggested reportable symptoms", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Yes", "text": "Yes", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "  A report must be submitted within 24 hours via the Ministry of Education’s Campus Safety and Disaster Prevention Information Network.", "text": "  A report must be submitted within 24 hours via the Ministry of Education’s Campus Safety and Disaster Prevention Information Network.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "  A report must also be submitted within 48 hours through Chiayi County’s School Suspected Infectious Disease Reporting System.", "text": "  A report must also be submitted within 48 hours through Chiayi County’s School Suspected Infectious Disease Reporting System.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Health Bureau:", "text": "Health Bureau:", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Upon receiving a report from the kindergarten, conduct a preliminary assessment of the case, and depending on the situation and type of illness, carry out an epidemiological investigation and report to the Centers for Disease Control.", "text": "Upon receiving a report from the kindergarten, conduct a preliminary assessment of the case, and depending on the situation and type of illness, carry out an epidemiological investigation and report to the Centers for Disease Control.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "If necessary, provide health education and important reminders at the kindergarten, or notify the individual to undergo specimen collection.", "text": "If necessary, provide health education and important reminders at the kindergarten, or notify the individual to undergo specimen collection.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "Implement appropriate epidemic prevention measures in accordance with the Communicable Disease Control Act.", "text": "Implement appropriate epidemic prevention measures in accordance with the Communicable Disease Control Act.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "", "text": "", "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Department of Education:\nCollaborate with the Health Bureau in conducting epidemiological investigations and assist Health Bureau personnel in implementing necessary epidemic prevention measures at all school levels.", "text": "Department of Education:\nCollaborate with the Health Bureau in conducting epidemiological investigations and assist Health Bureau personnel in implementing necessary epidemic prevention measures at all school levels.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The Health Bureau will handle", "text": "The Health Bureau will handle", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "reporting and specimen collection", "text": "reporting and specimen collection", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/46", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": ".", "text": ".", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/47", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/48", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/49", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/50", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/51", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/52", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Whether the epidemic has eased.", "text": "Whether the epidemic has eased.", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/53", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/54", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/55", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/56", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Whether the test results are positive for a legally designated infectious disease.", "text": "Whether the test results are positive for a legally designated infectious disease.", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/57", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "No", "text": "No", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/58", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/59", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/60", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/61", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/62", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/63", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Case closed.", "text": "Case closed.", "formatting": {"bold": true, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/64", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/65", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/66", "parent": {"$ref": "#/groups/15"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "The Health Bureau will carry out subsequent related epidemic prevention measures and follow-up, and will request assistance from the Centers for Disease Control if necessary.", "text": "The Health Bureau will carry out subsequent related epidemic prevention measures and follow-up, and will request assistance from the Centers for Disease Control if necessary.", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/67", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/68", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/69", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/70", "parent": {"$ref": "#/body"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}], "pictures": [], "tables": [], "key_value_items": [], "form_items": [], "pages": {}}