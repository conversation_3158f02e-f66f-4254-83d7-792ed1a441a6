{"schema_name": "DoclingDocument", "version": "1.3.0", "name": "unit_test_lists", "origin": {"mimetype": "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "binary_hash": 13601004233111293776, "filename": "unit_test_lists.docx"}, "furniture": {"self_ref": "#/furniture", "children": [], "content_layer": "furniture", "name": "_root_", "label": "unspecified"}, "body": {"self_ref": "#/body", "children": [{"$ref": "#/groups/0"}], "content_layer": "body", "name": "_root_", "label": "unspecified"}, "groups": [{"self_ref": "#/groups/0", "parent": {"$ref": "#/body"}, "children": [{"$ref": "#/texts/0"}], "content_layer": "body", "name": "header-0", "label": "section"}, {"self_ref": "#/groups/1", "parent": {"$ref": "#/texts/0"}, "children": [{"$ref": "#/texts/7"}, {"$ref": "#/texts/12"}, {"$ref": "#/texts/17"}, {"$ref": "#/texts/25"}, {"$ref": "#/texts/30"}, {"$ref": "#/texts/36"}], "content_layer": "body", "name": "header-2", "label": "section"}, {"self_ref": "#/groups/2", "parent": {"$ref": "#/texts/7"}, "children": [{"$ref": "#/texts/8"}, {"$ref": "#/texts/9"}, {"$ref": "#/texts/10"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/3", "parent": {"$ref": "#/texts/12"}, "children": [{"$ref": "#/texts/13"}, {"$ref": "#/texts/14"}, {"$ref": "#/texts/15"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/4", "parent": {"$ref": "#/texts/17"}, "children": [{"$ref": "#/texts/18"}, {"$ref": "#/texts/19"}, {"$ref": "#/groups/5"}, {"$ref": "#/texts/23"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/5", "parent": {"$ref": "#/groups/4"}, "children": [{"$ref": "#/texts/20"}, {"$ref": "#/texts/21"}, {"$ref": "#/texts/22"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/6", "parent": {"$ref": "#/texts/25"}, "children": [{"$ref": "#/texts/26"}, {"$ref": "#/groups/7"}, {"$ref": "#/texts/28"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/7", "parent": {"$ref": "#/groups/6"}, "children": [{"$ref": "#/texts/27"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/8", "parent": {"$ref": "#/texts/30"}, "children": [{"$ref": "#/texts/31"}, {"$ref": "#/groups/9"}, {"$ref": "#/texts/34"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/9", "parent": {"$ref": "#/groups/8"}, "children": [{"$ref": "#/texts/32"}, {"$ref": "#/groups/10"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/10", "parent": {"$ref": "#/groups/9"}, "children": [{"$ref": "#/texts/33"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/11", "parent": {"$ref": "#/texts/36"}, "children": [{"$ref": "#/texts/37"}, {"$ref": "#/texts/38"}, {"$ref": "#/groups/12"}, {"$ref": "#/texts/42"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/12", "parent": {"$ref": "#/groups/11"}, "children": [{"$ref": "#/texts/39"}, {"$ref": "#/texts/40"}, {"$ref": "#/groups/13"}], "content_layer": "body", "name": "list", "label": "list"}, {"self_ref": "#/groups/13", "parent": {"$ref": "#/groups/12"}, "children": [{"$ref": "#/texts/41"}], "content_layer": "body", "name": "list", "label": "list"}], "texts": [{"self_ref": "#/texts/0", "parent": {"$ref": "#/groups/0"}, "children": [{"$ref": "#/texts/1"}, {"$ref": "#/texts/2"}, {"$ref": "#/texts/3"}, {"$ref": "#/texts/4"}, {"$ref": "#/texts/5"}, {"$ref": "#/texts/6"}, {"$ref": "#/groups/1"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test Document", "text": "Test Document", "level": 1}, {"self_ref": "#/texts/1", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/2", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/3", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Paragraph 2.1.1", "text": "Paragraph 2.1.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/4", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/5", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "Paragraph 2.1.2", "text": "Paragraph 2.1.2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}}, {"self_ref": "#/texts/6", "parent": {"$ref": "#/texts/0"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/7", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/2"}, {"$ref": "#/texts/11"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test 1:", "text": "Test 1:", "level": 3}, {"self_ref": "#/texts/8", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1", "text": "List item 1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/9", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 2", "text": "List item 2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/10", "parent": {"$ref": "#/groups/2"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 3", "text": "List item 3", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/11", "parent": {"$ref": "#/texts/7"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/12", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/3"}, {"$ref": "#/texts/16"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test 2:", "text": "Test 2:", "level": 3}, {"self_ref": "#/texts/13", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item a", "text": "List item a", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/14", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item b", "text": "List item b", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/15", "parent": {"$ref": "#/groups/3"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item c", "text": "List item c", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/16", "parent": {"$ref": "#/texts/12"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/17", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/4"}, {"$ref": "#/texts/24"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test 3:", "text": "Test 3:", "level": 3}, {"self_ref": "#/texts/18", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1", "text": "List item 1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/19", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 2", "text": "List item 2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/20", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.1", "text": "List item 1.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/21", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.2", "text": "List item 1.2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/22", "parent": {"$ref": "#/groups/5"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.3", "text": "List item 1.3", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/23", "parent": {"$ref": "#/groups/4"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 3", "text": "List item 3", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/24", "parent": {"$ref": "#/texts/17"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/25", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/6"}, {"$ref": "#/texts/29"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test 4:", "text": "Test 4:", "level": 3}, {"self_ref": "#/texts/26", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1", "text": "List item 1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/27", "parent": {"$ref": "#/groups/7"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.1", "text": "List item 1.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/28", "parent": {"$ref": "#/groups/6"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 2", "text": "List item 2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/29", "parent": {"$ref": "#/texts/25"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/30", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/8"}, {"$ref": "#/texts/35"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test 5:", "text": "Test 5:", "level": 3}, {"self_ref": "#/texts/31", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1", "text": "List item 1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/32", "parent": {"$ref": "#/groups/9"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.1", "text": "List item 1.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/33", "parent": {"$ref": "#/groups/10"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.1.1", "text": "List item 1.1.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/34", "parent": {"$ref": "#/groups/8"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 3", "text": "List item 3", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/35", "parent": {"$ref": "#/texts/30"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/36", "parent": {"$ref": "#/groups/1"}, "children": [{"$ref": "#/groups/11"}, {"$ref": "#/texts/43"}, {"$ref": "#/texts/44"}, {"$ref": "#/texts/45"}], "content_layer": "body", "label": "section_header", "prov": [], "orig": "Test 6:", "text": "Test 6:", "level": 3}, {"self_ref": "#/texts/37", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1", "text": "List item 1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/38", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 2", "text": "List item 2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/39", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.1", "text": "List item 1.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/40", "parent": {"$ref": "#/groups/12"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.2", "text": "List item 1.2", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/41", "parent": {"$ref": "#/groups/13"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 1.2.1", "text": "List item 1.2.1", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/42", "parent": {"$ref": "#/groups/11"}, "children": [], "content_layer": "body", "label": "list_item", "prov": [], "orig": "List item 3", "text": "List item 3", "formatting": {"bold": false, "italic": false, "underline": false, "strikethrough": false}, "enumerated": false, "marker": "-"}, {"self_ref": "#/texts/43", "parent": {"$ref": "#/texts/36"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/44", "parent": {"$ref": "#/texts/36"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}, {"self_ref": "#/texts/45", "parent": {"$ref": "#/texts/36"}, "children": [], "content_layer": "body", "label": "paragraph", "prov": [], "orig": "", "text": ""}], "pictures": [], "tables": [], "key_value_items": [], "form_items": [], "pages": {}}