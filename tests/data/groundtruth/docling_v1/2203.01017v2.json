{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "2203.01017v2.pdf", "filename-prov": null, "document-hash": "00be757f6bc94e7f75134e02e196d1c73ff960d52abb081a93104bba5a6470ff", "#-pages": 16, "collection-name": null, "description": null, "page-hashes": [{"hash": "56046b8a75e14ce81bf22f568f726b2bd7b8f82a885dbff3260d8945699796e4", "model": "default", "page": 1}, {"hash": "80f9116b4f8c9b08dc9763877dabaf65affef9c4a9a0f91a3673e8360aa95713", "model": "default", "page": 2}, {"hash": "8b422eca6675fcaa4919295eb9af624e581041dad8a5903bec7cb26254f2b59b", "model": "default", "page": 3}, {"hash": "a7f7a5799d183e4f3d0d8e917c3df1acd344e0abfaedebb9ed04f2f67844ad95", "model": "default", "page": 4}, {"hash": "14036095ac3b7fb802ff5c061cf5584f482de800aff7ed4eccfb67e5a89e1ba2", "model": "default", "page": 5}, {"hash": "633c440068e406a17f913cac0c2e3f0606f66111994bd2940726a56ea37274a0", "model": "default", "page": 6}, {"hash": "fb67646dad9c7255e55f305b59fb6f697fcf26875e085b63ac0729916ce60b6c", "model": "default", "page": 7}, {"hash": "acc79b977714a917605f6530c1df05f73f4cc0aa5d73b506fffe6287ae19a807", "model": "default", "page": 8}, {"hash": "bdb376928a3150909023df34ff94cd1eb12e1f90ae03a72834b74433ef498205", "model": "default", "page": 9}, {"hash": "df60cff4949de8851338c4fd85ad43f534cee1da4772f36b74b2341cad6ec5c9", "model": "default", "page": 10}, {"hash": "3df470edc2c1a275cfa920f7487a89fcb1825a9b009e85386b199cbfe80aff73", "model": "default", "page": 11}, {"hash": "f986169f2c0ff7997ccec2d71833cea7c5df4a641b92996c3f8fba9563441ad1", "model": "default", "page": 12}, {"hash": "ba03af2311ea8d68bf53cccda386ebf4ed68f14943fc421417799401a2afe95a", "model": "default", "page": 13}, {"hash": "1d63990cced2905e29696a09ad1bca47fcd15d584be32b4c7ab76c2f47f75d92", "model": "default", "page": 14}, {"hash": "fc40b9fb3698f24af7beda03b7afac10c3fcc6c73e83b4c6159785ea2991e2c4", "model": "default", "page": 15}, {"hash": "96080fce6eb8572fe319782f353a67661947f48e67607b1ffd8c01d617d075a7", "model": "default", "page": 16}]}, "main-text": [{"prov": [{"bbox": [96.301003, 672.06866, 498.92708999999996, 684.96588], "page": 1, "span": [0, 61], "__ref_s3_data": null}], "text": "TableFormer: Table Structure Understanding with Transformers.", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [142.47701, 620.67963, 452.75027, 645.31464], "page": 1, "span": [0, 73], "__ref_s3_data": null}], "text": "<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> Research", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [208.123, 607.57446, 378.73257, 616.03876], "page": 1, "span": [0, 35], "__ref_s3_data": null}], "text": "{ ahn,nli,mly,taa } @zurich.ibm.com", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [145.99498, 565.76929, 190.48029, 576.51703], "page": 1, "span": [0, 8], "__ref_s3_data": null}], "text": "Abstract", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [315.56702, 565.24518, 408.4407, 573.99316], "page": 1, "span": [0, 22], "__ref_s3_data": null}], "text": "a. Picture of a table:", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111977, 279.00335999999993, 286.36511, 550.60492], "page": 1, "span": [0, 1320], "__ref_s3_data": null}], "text": "Tables organize valuable content in a concise and compact representation. This content is extremely valuable for systems such as search engines, Knowledge Graph's, etc, since they enhance their predictive capabilities. Unfortunately, tables come in a large variety of shapes and sizes. Furthermore, they can have complex column/row-header configurations, multiline rows, different variety of separation lines, missing entries, etc. As such, the correct identification of the table-structure from an image is a nontrivial task. In this paper, we present a new table-structure identification model. The latter improves the latest end-toend deep learning model (i.e. encoder-dual-decoder from PubTabNet) in two significant ways. First, we introduce a new object detection decoder for table-cells. In this way, we can obtain the content of the table-cells from programmatic PDF's directly from the PDF source and avoid the training of the custom OCR decoders. This architectural change leads to more accurate table-content extraction and allows us to tackle non-english tables. Second, we replace the LSTM decoders with transformer based decoders. This upgrade improves significantly the previous state-of-the-art tree-editing-distance-score (TEDS) from 91% to 98.5% on simple tables and from 88.7% to 95% on complex tables.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111977, 241.30951000000005, 126.94804, 252.05723999999998], "page": 1, "span": [0, 15], "__ref_s3_data": null}], "text": "1. Introduction", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111977, 78.84822099999997, 286.36508, 231.21680000000003], "page": 1, "span": [0, 712], "__ref_s3_data": null}], "text": "The occurrence of tables in documents is ubiquitous. They often summarise quantitative or factual data, which is cumbersome to describe in verbose text but nevertheless extremely valuable. Unfortunately, this compact representation is often not easy to parse by machines. There are many implicit conventions used to obtain a compact table representation. For example, tables often have complex columnand row-headers in order to reduce duplicated cell content. Lines of different shapes and sizes are leveraged to separate content or indicate a tree structure. Additionally, tables can also have empty/missing table-entries or multi-row textual table-entries. Fig. 1 shows a table which presents all these issues.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/0"}, {"name": "Table", "type": "table", "$ref": "#/tables/0"}, {"prov": [{"bbox": [315.56702, 458.7572, 486.40194999999994, 478.30521000000005], "page": 1, "span": [0, 68], "__ref_s3_data": null}], "text": "- b. Red-annotation of bounding boxes, Blue-predictions by TableFormer", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [315.56702, 363.06918, 491.1912500000001, 371.8172], "page": 1, "span": [0, 38], "__ref_s3_data": null}], "text": "- c. Structure predicted by TableFormer:", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/1"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/2"}, {"prov": [{"bbox": [308.862, 232.72709999999995, 545.11517, 277.49963], "page": 1, "span": [0, 220], "__ref_s3_data": null}], "text": "Figure 1: Picture of a table with subtle, complex features such as (1) multi-column headers, (2) cell with multi-row text and (3) cells with no content. Image from PubTabNet evaluation set, filename: 'PMC2944238 004 02'.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/1"}, {"prov": [{"bbox": [308.862, 126.95307000000003, 545.11517, 207.59064], "page": 1, "span": [0, 363], "__ref_s3_data": null}], "text": "Recently, significant progress has been made with vision based approaches to extract tables in documents. For the sake of completeness, the issue of table extraction from documents is typically decomposed into two separate challenges, i.e. (1) finding the location of the table(s) on a document-page and (2) finding the structure of a given table in the document.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 78.**************, 545.11511, 123.61964], "page": 1, "span": [0, 229], "__ref_s3_data": null}], "text": "The first problem is called table-location and has been previously addressed [30, 38, 19, 21, 23, 26, 8] with stateof-the-art object-detection networks (e.g. YOLO and later on Mask-RCNN [9]). For all practical purposes, it can be", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 695.93005, 286.36505, 716.79163], "page": 2, "span": [0, 75], "__ref_s3_data": null}], "text": "considered as a solved problem, given enough ground-truth data to train on.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 563.96991, 286.36514, 692.42859], "page": 2, "span": [0, 626], "__ref_s3_data": null}], "text": "The second problem is called table-structure decomposition. The latter is a long standing problem in the community of document understanding [6, 4, 14]. Contrary to the table-location problem, there are no commonly used approaches that can easily be re-purposed to solve this problem. Lately, a set of new model-architectures has been proposed by the community to address table-structure decomposition [37, 36, 18, 20]. All these models have some weaknesses (see Sec. 2). The common denominator here is the reliance on textual features and/or the inability to provide the bounding box of each table-cell in the original image.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 420.05493, 286.36511, 560.46844], "page": 2, "span": [0, 643], "__ref_s3_data": null}], "text": "In this paper, we want to address these weaknesses and present a robust table-structure decomposition algorithm. The design criteria for our model are the following. First, we want our algorithm to be language agnostic. In this way, we can obtain the structure of any table, irregardless of the language. Second, we want our algorithm to leverage as much data as possible from the original PDF document. For programmatic PDF documents, the text-cells can often be extracted much faster and with higher accuracy compared to OCR methods. Last but not least, we want to have a direct link between the table-cell and its bounding box in the image.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 359.827, 286.36658, 416.5535], "page": 2, "span": [0, 242], "__ref_s3_data": null}], "text": "To meet the design criteria listed above, we developed a new model called TableFormer and a synthetically generated table structure dataset called SynthTabNet $^{1}$. In particular, our contributions in this work can be summarised as follows:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [61.569016, 302.67703, 286.3649, 347.56812], "page": 2, "span": [0, 166], "__ref_s3_data": null}], "text": "- · We propose TableFormer , a transformer based model that predicts tables structure and bounding boxes for the table content simultaneously in an end-to-end approach.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [61.569016, 245.07404999999994, 286.3649, 289.96616], "page": 2, "span": [0, 181], "__ref_s3_data": null}], "text": "- · Across all benchmark datasets TableFormer significantly outperforms existing state-of-the-art metrics, while being much more efficient in training and inference to existing works.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [61.569, 199.42705, 286.36493, 232.36316], "page": 2, "span": [0, 106], "__ref_s3_data": null}], "text": "- · We present SynthTabNet a synthetically generated dataset, with various appearance styles and complexity.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [61.***************, 153.**************, 286.36508, 186.**************], "page": 2, "span": [0, 131], "__ref_s3_data": null}], "text": "- · An augmented dataset based on PubTabNet [37], FinTabNet [36], and TableBank [17] with generated ground-truth for reproducibility.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 96.630043, 286.36511, 141.40161], "page": 2, "span": [0, 231], "__ref_s3_data": null}], "text": "The paper is structured as follows. In Sec. 2, we give a brief overview of the current state-of-the-art. In Sec. 3, we describe the datasets on which we train. In Sec. 4, we introduce the TableFormer model-architecture and describe", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [60.**************, 79.278458, 183.73055, 86.**************], "page": 2, "span": [0, 40], "__ref_s3_data": null}], "text": "$^{1}$https://github.com/IBM/SynthTabNet", "type": "footnote", "payload": null, "name": "Footnote", "font": null}, {"prov": [{"bbox": [308.862, 683.*************, 545.11511, 716.79163], "page": 2, "span": [0, 166], "__ref_s3_data": null}], "text": "its results & performance in Sec. 5. As a conclusion, we describe how this new model-architecture can be re-purposed for other tasks in the computer-vision community.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 659.52032, 498.28021, 670.26807], "page": 2, "span": [0, 37], "__ref_s3_data": null}], "text": "2. Previous work and State of the Art", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [308.862, 461.54498, 545.11517, 649.77863], "page": 2, "span": [0, 901], "__ref_s3_data": null}], "text": "Identifying the structure of a table has been an outstanding problem in the document-parsing community, that motivates many organised public challenges [6, 4, 14]. The difficulty of the problem can be attributed to a number of factors. First, there is a large variety in the shapes and sizes of tables. Such large variety requires a flexible method. This is especially true for complex column- and row headers, which can be extremely intricate and demanding. A second factor of complexity is the lack of data with regard to table-structure. Until the publication of PubTabNet [37], there were no large datasets (i.e. > 100 K tables) that provided structure information. This happens primarily due to the fact that tables are notoriously time-consuming to annotate by hand. However, this has definitely changed in recent years with the deliverance of PubTabNet [37], FinTabNet [36], TableBank [17] etc.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 341.*************, 545.11523, 458.43054], "page": 2, "span": [0, 552], "__ref_s3_data": null}], "text": "Before the rising popularity of deep neural networks, the community relied heavily on heuristic and/or statistical methods to do table structure identification [3, 7, 11, 5, 13, 28]. Although such methods work well on constrained tables [12], a more data-driven approach can be applied due to the advent of convolutional neural networks (CNNs) and the availability of large datasets. To the best-of-our knowledge, there are currently two different types of network architecture that are being pursued for state-of-the-art tablestructure identification.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86197, 78.**************, 545.11688, 338.93222], "page": 2, "span": [0, 1262], "__ref_s3_data": null}], "text": "Image-to-Text networks : In this type of network, one predicts a sequence of tokens starting from an encoded image. Such sequences of tokens can be HTML table tags [37, 17] or LaTeX symbols[10]. The choice of symbols is ultimately not very important, since one can be transformed into the other. There are however subtle variations in the Image-to-Text networks. The easiest network architectures are \"image-encoder → text-decoder\" (IETD), similar to network architectures that try to provide captions to images [32]. In these IETD networks, one expects as output the LaTeX/HTML string of the entire table, i.e. the symbols necessary for creating the table with the content of the table. Another approach is the \"image-encoder → dual decoder\" (IEDD) networks. In these type of networks, one has two consecutive decoders with different purposes. The first decoder is the tag-decoder , i.e. it only produces the HTML/LaTeX tags which construct an empty table. The second content-decoder uses the encoding of the image in combination with the output encoding of each cell-tag (from the tag-decoder ) to generate the textual content of each table cell. The network architecture of IEDD is certainly more elaborate, but it has the advantage that one can pre-train the", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 707.88507, 250.15102, 716.79163], "page": 3, "span": [0, 51], "__ref_s3_data": null}], "text": "tag-decoder which is constrained to the table-tags.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 516.5459, 286.36514, 704.7806400000001], "page": 3, "span": [0, 864], "__ref_s3_data": null}], "text": "In practice, both network architectures (IETD and IEDD) require an implicit, custom trained object-characterrecognition (OCR) to obtain the content of the table-cells. In the case of IETD, this OCR engine is implicit in the decoder similar to [24]. For the IEDD, the OCR is solely embedded in the content-decoder. This reliance on a custom, implicit OCR decoder is of course problematic. OCR is a well known and extremely tough problem, that often needs custom training for each individual language. However, the limited availability for non-english content in the current datasets, makes it impractical to apply the IETD and IEDD methods on tables with other languages. Additionally, OCR can be completely omitted if the tables originate from programmatic PDF documents with known positions of each cell. The latter was the inspiration for the work of this paper.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111992, 301.29712, 286.36511, 513.56104], "page": 3, "span": [0, 1007], "__ref_s3_data": null}], "text": "Graph Neural networks : Graph Neural networks (GNN's) take a radically different approach to tablestructure extraction. Note that one table cell can constitute out of multiple text-cells. To obtain the table-structure, one creates an initial graph, where each of the text-cells becomes a node in the graph similar to [33, 34, 2]. Each node is then associated with en embedding vector coming from the encoded image, its coordinates and the encoded text. Furthermore, nodes that represent adjacent text-cells are linked. Graph Convolutional Networks (GCN's) based methods take the image as an input, but also the position of the text-cells and their content [18]. The purpose of a GCN is to transform the input graph into a new graph, which replaces the old links with new ones. The new links then represent the table-structure. With this approach, one can avoid the need to build custom OCR decoders. However, the quality of the reconstructed structure is not comparable to the current state-of-the-art [18].", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111984, 169.73315000000002, 286.36627, 298.31125], "page": 3, "span": [0, 619], "__ref_s3_data": null}], "text": "Hybrid Deep Learning-Rule-Based approach : A popular current model for table-structure identification is the use of a hybrid Deep Learning-Rule-Based approach similar to [27, 29]. In this approach, one first detects the position of the table-cells with object detection (e.g. YoloVx or MaskRCNN), then classifies the table into different types (from its images) and finally uses different rule-sets to obtain its table-structure. Currently, this approach achieves stateof-the-art results, but is not an end-to-end deep-learning method. As such, new rules need to be written if different types of tables are encountered.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111984, 145.**************, 105.22546, 156.05516], "page": 3, "span": [0, 11], "__ref_s3_data": null}], "text": "3. Datasets", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111984, 78.**************, 286.36508, 135.**************], "page": 3, "span": [0, 281], "__ref_s3_data": null}], "text": "We rely on large-scale datasets such as PubTabNet [37], FinTabNet [36], and TableBank [17] datasets to train and evaluate our models. These datasets span over various appearance styles and content. We also introduce our own synthetically generated SynthTabNet dataset to fix an im-", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/3"}, {"prov": [{"bbox": [308.862, 503.*************, 545.11511, 524.16364], "page": 3, "span": [0, 104], "__ref_s3_data": null}], "text": "Figure 2: Distribution of the tables across different table dimensions in PubTabNet + FinTabNet datasets", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [308.862, 465.62009, 437.27002, 474.52664], "page": 3, "span": [0, 33], "__ref_s3_data": null}], "text": "balance in the previous datasets.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 164.**************, 545.11517, 460.46863], "page": 3, "span": [0, 1400], "__ref_s3_data": null}], "text": "The PubTabNet dataset contains 509k tables delivered as annotated PNG images. The annotations consist of the table structure represented in HTML format, the tokenized text and its bounding boxes per table cell. Fig. 1 shows the appearance style of PubTabNet. Depending on its complexity, a table is characterized as \"simple\" when it does not contain row spans or column spans, otherwise it is \"complex\". The dataset is divided into Train and Val splits (roughly 98% and 2%). The Train split consists of 54% simple and 46% complex tables and the Val split of 51% and 49% respectively. The FinTabNet dataset contains 112k tables delivered as single-page PDF documents with mixed table structures and text content. Similarly to the PubTabNet, the annotations of FinTabNet include the table structure in HTML, the tokenized text and the bounding boxes on a table cell basis. The dataset is divided into Train, Test and Val splits (81%, 9.5%, 9.5%), and each one is almost equally divided into simple and complex tables (Train: 48% simple, 52% complex, Test: 48% simple, 52% complex, Test: 53% simple, 47% complex). Finally the TableBank dataset consists of 145k tables provided as JPEG images. The latter has annotations for the table structure, but only few with bounding boxes of the table cells. The entire dataset consists of simple tables and it is divided into 90% Train, 3% Test and 7% Val splits.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 78.**************, 545.11511, 159.**********0001], "page": 3, "span": [0, 406], "__ref_s3_data": null}], "text": "Due to the heterogeneity across the dataset formats, it was necessary to combine all available data into one homogenized dataset before we could train our models for practical purposes. Given the size of PubTabNet, we adopted its annotation format and we extracted and converted all tables as PNG images with a resolution of 72 dpi. Additionally, we have filtered out tables with extreme sizes due to small", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 695.93005, 286.36511, 716.79163], "page": 4, "span": [0, 93], "__ref_s3_data": null}], "text": "amount of such tables, and kept only those ones ranging between 1*1 and 20*10 (rows/columns).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 478.89493, 286.36514, 691.03961], "page": 4, "span": [0, 983], "__ref_s3_data": null}], "text": "The availability of the bounding boxes for all table cells is essential to train our models. In order to distinguish between empty and non-empty bounding boxes, we have introduced a binary class in the annotation. Unfortunately, the original datasets either omit the bounding boxes for whole tables (e.g. TableBank) or they narrow their scope only to non-empty cells. Therefore, it was imperative to introduce a data pre-processing procedure that generates the missing bounding boxes out of the annotation information. This procedure first parses the provided table structure and calculates the dimensions of the most fine-grained grid that covers the table structure. Notice that each table cell may occupy multiple grid squares due to row or column spans. In case of PubTabNet we had to compute missing bounding boxes for 48% of the simple and 69% of the complex tables. Regarding FinTabNet, 68% of the simple and 98% of the complex tables require the generation of bounding boxes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 357.50104, 286.36511, 474.**************], "page": 4, "span": [0, 571], "__ref_s3_data": null}], "text": "As it is illustrated in Fig. 2, the table distributions from all datasets are skewed towards simpler structures with fewer number of rows/columns. Additionally, there is very limited variance in the table styles, which in case of PubTabNet and FinTabNet means one styling format for the majority of the tables. Similar limitations appear also in the type of table content, which in some cases (e.g. FinTabNet) is restricted to a certain domain. Ultimately, the lack of diversity in the training dataset damages the ability of the models to generalize well on unseen data.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 164.37611000000004, 286.36655, 352.6106], "page": 4, "span": [0, 941], "__ref_s3_data": null}], "text": "Motivated by those observations we aimed at generating a synthetic table dataset named SynthTabNet . This approach offers control over: 1) the size of the dataset, 2) the table structure, 3) the table style and 4) the type of content. The complexity of the table structure is described by the size of the table header and the table body, as well as the percentage of the table cells covered by row spans and column spans. A set of carefully designed styling templates provides the basis to build a wide range of table appearances. Lastly, the table content is generated out of a curated collection of text corpora. By controlling the size and scope of the synthetic datasets we are able to train and evaluate our models in a variety of different conditions. For example, we can first generate a highly diverse dataset to train our models and then evaluate their performance on other synthetic datasets which are focused on a specific domain.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112015, 78.84810600000003, 286.36511, 159.48567000000003], "page": 4, "span": [0, 405], "__ref_s3_data": null}], "text": "In this regard, we have prepared four synthetic datasets, each one containing 150k examples. The corpora to generate the table text consists of the most frequent terms appearing in PubTabNet and FinTabNet together with randomly generated text. The first two synthetic datasets have been fine-tuned to mimic the appearance of the original datasets but encompass more complicated table structures. The third", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/2"}, {"prov": [{"bbox": [308.862, 567.61102, 545.11505, 624.33862], "page": 4, "span": [0, 267], "__ref_s3_data": null}], "text": "Table 1: Both \"Combined-Tabnet\" and \"CombinedTabnet\" are variations of the following: (*) The CombinedTabnet dataset is the processed combination of PubTabNet and Fintabnet. (**) The combined dataset is the processed combination of PubTabNet, Fintabnet and TableBank.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [308.862, 497.60803, 545.11517, 542.37958], "page": 4, "span": [0, 210], "__ref_s3_data": null}], "text": "one adopts a colorful appearance with high contrast and the last one contains tables with sparse content. Lastly, we have combined all synthetic datasets into one big unified synthetic dataset of 600k examples.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [320.81699, 485.32104, 542.74396, 494.**************], "page": 4, "span": [0, 57], "__ref_s3_data": null}], "text": "Tab. 1 summarizes the various attributes of the datasets.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 460.**************, 444.**************, 470.81604], "page": 4, "span": [0, 24], "__ref_s3_data": null}], "text": "4. The TableFormer model", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [308.862, 345.51314999999994, 545.11523, 450.06061], "page": 4, "span": [0, 504], "__ref_s3_data": null}], "text": "Given the image of a table, <PERSON><PERSON><PERSON><PERSON> is able to predict: 1) a sequence of tokens that represent the structure of a table, and 2) a bounding box coupled to a subset of those tokens. The conversion of an image into a sequence of tokens is a well-known task [35, 16]. While attention is often used as an implicit method to associate each token of the sequence with a position in the original image, an explicit association between the individual table-cells and the image bounding boxes is also required.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 324.45367, 420.16058, 334.30573], "page": 4, "span": [0, 24], "__ref_s3_data": null}], "text": "4.1. Model architecture.", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [308.86197, 127.00018999999998, 545.11572, 315.23471], "page": 4, "span": [0, 907], "__ref_s3_data": null}], "text": "We now describe in detail the proposed method, which is composed of three main components, see Fig. 4. Our CNN Backbone Network encodes the input as a feature vector of predefined length. The input feature vector of the encoded image is passed to the Structure Decoder to produce a sequence of HTML tags that represent the structure of the table. With each prediction of an HTML standard data cell (' < td > ') the hidden state of that cell is passed to the Cell BBox Decoder. As for spanning cells, such as row or column span, the tag is broken down to ' < ', 'rowspan=' or 'colspan=', with the number of spanning cells (attribute), and ' > '. The hidden state attached to ' < ' is passed to the Cell BBox Decoder. A shared feed forward network (FFN) receives the hidden states from the Structure Decoder, to provide the final detection predictions of the bounding box coordinates and their classification.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86197, 78.84818300000006, 545.11511, 123.73929999999996], "page": 4, "span": [0, 223], "__ref_s3_data": null}], "text": "CNN Backbone Network. A ResNet-18 CNN is the backbone that receives the table image and encodes it as a vector of predefined length. The network has been modified by removing the linear and pooling layer, as we are not per-", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/4"}, {"prov": [{"bbox": [50.111992, 567.03308, 545.10846, 588.01422], "page": 5, "span": [0, 212], "__ref_s3_data": null}], "text": "Figure 3: <PERSON><PERSON><PERSON><PERSON> takes in an image of the PDF and creates bounding box and HTML structure predictions that are synchronized. The bounding boxes grabs the content from the PDF and inserts it in the structure.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/5"}, {"prov": [{"bbox": [50.112, 111.72906, 286.36597, 264.2171900000001], "page": 5, "span": [0, 745], "__ref_s3_data": null}], "text": "Figure 4: Given an input image of a table, the Encoder produces fixed-length features that represent the input image. The features are then passed to both the Structure Decoder and Cell BBox Decoder . During training, the Structure Decoder receives 'tokenized tags' of the HTML code that represent the table structure. Afterwards, a transformer encoder and decoder architecture is employed to produce features that are received by a linear layer, and the Cell BBox Decoder. The linear layer is applied to the features to predict the tags. Simultaneously, the Cell BBox Decoder selects features referring to the data cells (' < td > ', ' < ') and passes them through an attention network, an MLP, and a linear layer to predict the bounding boxes.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [308.862, 497.6930500000001, 545.11505, 542.46558], "page": 5, "span": [0, 227], "__ref_s3_data": null}], "text": "forming classification, and adding an adaptive pooling layer of size 28*28. ResNet by default downsamples the image resolution by 32 and then the encoded image is provided to both the Structure Decoder , and Cell BBox Decoder .", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86194, 378.03818, 545.11511, 494.66019], "page": 5, "span": [0, 563], "__ref_s3_data": null}], "text": "Structure Decoder. The transformer architecture of this component is based on the work proposed in [31]. After extensive experimentation, the Structure Decoder is modeled as a transformer encoder with two encoder layers and a transformer decoder made from a stack of 4 decoder layers that comprise mainly of multi-head attention and feed forward layers. This configuration uses fewer layers and heads in comparison to networks applied to other problems (e.g. \"Scene Understanding\", \"Image Captioning\"), something which we relate to the simplicity of table images.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86197, 246.42728999999997, 545.11511, 374.88574], "page": 5, "span": [0, 592], "__ref_s3_data": null}], "text": "The transformer encoder receives an encoded image from the CNN Backbone Network and refines it through a multi-head dot-product attention layer, followed by a Feed Forward Network. During training, the transformer decoder receives as input the output feature produced by the transformer encoder, and the tokenized input of the HTML ground-truth tags. Using a stack of multi-head attention layers, different aspects of the tag sequence could be inferred. This is achieved by each attention head on a layer operating in a different subspace, and then combining altogether their attention score.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86194, 138.72729000000004, 545.11511, 243.3954], "page": 5, "span": [0, 483], "__ref_s3_data": null}], "text": "Cell BBox Decoder. Our architecture allows to simultaneously predict HTML tags and bounding boxes for each table cell without the need of a separate object detector end to end. This approach is inspired by DETR [1] which employs a Transformer Encoder, and Decoder that looks for a specific number of object queries (potential object detections). As our model utilizes a transformer architecture, the hidden state of the < td > ' and ' < ' HTML structure tags become the object query.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86194, 78.84827399999995, 545.11505, 135.57484], "page": 5, "span": [0, 286], "__ref_s3_data": null}], "text": "The encoding generated by the CNN Backbone Network along with the features acquired for every data cell from the Transformer Decoder are then passed to the attention network. The attention network takes both inputs and learns to provide an attention weighted encoding. This weighted at-", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 636.15399, 286.36514, 716.79163], "page": 6, "span": [0, 380], "__ref_s3_data": null}], "text": "tention encoding is then multiplied to the encoded image to produce a feature for each table cell. Notice that this is different than the typical object detection problem where imbalances between the number of detections and the amount of objects may exist. In our case, we know up front that the produced detections always match with the table cells in number and correspondence.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 551.73694, 286.36511, 632.37555], "page": 6, "span": [0, 371], "__ref_s3_data": null}], "text": "The output features for each table cell are then fed into the feed-forward network (FFN). The FFN consists of a Multi-Layer Perceptron (3 layers with ReLU activation function) that predicts the normalized coordinates for the bounding box of each table cell. Finally, the predicted bounding boxes are classified based on whether they are empty or not using a linear layer.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 347.7691, 286.36572, 548.07806], "page": 6, "span": [0, 985], "__ref_s3_data": null}], "text": "Loss Functions. We formulate a multi-task loss Eq. 2 to train our network. The Cross-Entropy loss (denoted as l$_{s}$ ) is used to train the Structure Decoder which predicts the structure tokens. As for the Cell BBox Decoder it is trained with a combination of losses denoted as l$_{box}$ . l$_{box}$ consists of the generally used l$_{1}$ loss for object detection and the IoU loss ( l$_{iou}$ ) to be scale invariant as explained in [25]. In comparison to DETR, we do not use the Hungarian algorithm [15] to match the predicted bounding boxes with the ground-truth boxes, as we have already achieved a one-toone match through two steps: 1) Our token input sequence is naturally ordered, therefore the hidden states of the table data cells are also in order when they are provided as input to the Cell BBox Decoder , and 2) Our bounding boxes generation mechanism (see Sec. 3) ensures a one-to-one mapping between the cell content and its bounding box for all post-processed datasets.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112022, 323.12811, 286.36499, 343.98965], "page": 6, "span": [0, 67], "__ref_s3_data": null}], "text": "The loss used to train the TableFormer can be defined as following:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [124.33002, 274.92828, 286.36243, 298.71906], "page": 6, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "equation", "payload": null, "name": "Formula", "font": null}, {"prov": [{"bbox": [50.11203, 251.78412000000003, 281.59692, 261.4079899999999], "page": 6, "span": [0, 76], "__ref_s3_data": null}], "text": "where λ ∈ [0, 1], and λ$_{iou}$, λ$_{l}$$_{1}$ ∈$_{R}$ are hyper-parameters.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112045, 225.33538999999996, 171.98335, 236.08311000000003], "page": 6, "span": [0, 23], "__ref_s3_data": null}], "text": "5. Experimental Results", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.112045, 205.88362000000006, 179.17502, 215.73567000000003], "page": 6, "span": [0, 27], "__ref_s3_data": null}], "text": "5.1. Implementation Details", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.112045, 151.49311999999998, 286.36517, 196.26567], "page": 6, "span": [0, 207], "__ref_s3_data": null}], "text": "TableFormer uses ResNet-18 as the CNN Backbone Network . The input images are resized to 448*448 pixels and the feature map has a dimension of 28*28. Additionally, we enforce the following input constraints:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [91.661049, 113.60411999999997, 286.36246, 138.17200000000003], "page": 6, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "equation", "payload": null, "name": "Formula", "font": null}, {"prov": [{"bbox": [50.112061, 78.84812199999999, 286.36514, 99.70968600000003], "page": 6, "span": [0, 274], "__ref_s3_data": null}, {"bbox": [50.112061, 78.84812199999999, 286.36514, 99.70968600000003], "page": 6, "span": [0, 274], "__ref_s3_data": null}], "text": "Although input constraints are used also by other methods, such as EDD, ours are less restrictive due to the improved runtime performance and lower memory footprint of TableFormer. This allows to utilize input samples with longer sequences and images with larger dimensions.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86206, 463.62595, 545.1153, 675.77063], "page": 6, "span": [0, 1024], "__ref_s3_data": null}], "text": "The Transformer Encoder consists of two \"Transformer Encoder Layers\", with an input feature size of 512, feed forward network of 1024, and 4 attention heads. As for the Transformer Decoder it is composed of four \"Transformer Decoder Layers\" with similar input and output dimensions as the \"Transformer Encoder Layers\". Even though our model uses fewer layers and heads than the default implementation parameters, our extensive experimentation has proved this setup to be more suitable for table images. We attribute this finding to the inherent design of table images, which contain mostly lines and text, unlike the more elaborate content present in other scopes (e.g. the COCO dataset). Moreover, we have added ResNet blocks to the inputs of the Structure Decoder and Cell BBox Decoder. This prevents a decoder having a stronger influence over the learned weights which would damage the other prediction task (structure vs bounding boxes), but learn task specific weights instead. Lastly our dropout layers are set to 0.5.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86203, 362.83002, 545.11517, 455.42249], "page": 6, "span": [0, 419], "__ref_s3_data": null}], "text": "For training, TableFormer is trained with 3 Adam optimizers, each one for the CNN Backbone Network , Structure Decoder , and Cell BBox Decoder . Taking the PubTabNet as an example for our parameter set up, the initializing learning rate is 0.001 for 12 epochs with a batch size of 24, and λ set to 0.5. Afterwards, we reduce the learning rate to 0.0001, the batch size to 18 and train for 12 more epochs or convergence.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86203, 238.12311, 545.11523, 354.62558000000007], "page": 6, "span": [0, 528], "__ref_s3_data": null}], "text": "TableFormer is implemented with PyTorch and Torchvision libraries [22]. To speed up the inference, the image undergoes a single forward pass through the CNN Backbone Network and transformer encoder. This eliminates the overhead of generating the same features for each decoding step. Similarly, we employ a 'caching' technique to preform faster autoregressive decoding. This is achieved by storing the features of decoded tokens so we can reuse them for each time step. Therefore, we only compute the attention for each new tag.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86203, 202.59362999999996, 397.44281, 212.44568000000004], "page": 6, "span": [0, 19], "__ref_s3_data": null}], "text": "5.2. Generalization", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [308.86203, 119.86811, 545.11517, 188.**************], "page": 6, "span": [0, 299], "__ref_s3_data": null}], "text": "TableFormer is evaluated on three major publicly available datasets of different nature to prove the generalization and effectiveness of our model. The datasets used for evaluation are the PubTabNet, FinTabNet and TableBank which stem from the scientific, financial and general domains respectively.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86203, 78.**************, 545.11523, 111.66467], "page": 6, "span": [0, 155], "__ref_s3_data": null}], "text": "We also share our baseline results on the challenging SynthTabNet dataset. Throughout our experiments, the same parameters stated in Sec. 5.1 are utilized.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 707.74658, 167.89825, 717.*************], "page": 7, "span": [0, 25], "__ref_s3_data": null}], "text": "5.3. Datasets and Metrics", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.112, 653.87701, 286.36511, 698.*************], "page": 7, "span": [0, 192], "__ref_s3_data": null}], "text": "The Tree-Edit-Distance-Based Similarity (TEDS) metric was introduced in [37]. It represents the prediction, and ground-truth as a tree structure of HTML tags. This similarity is calculated as:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [86.218994, 619.26123, 286.3624, 641.68201], "page": 7, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "equation", "payload": null, "name": "Formula", "font": null}, {"prov": [{"bbox": [50.111984, 578.021, 286.36285, 610.99701], "page": 7, "span": [0, 162], "__ref_s3_data": null}], "text": "where T$_{a}$ and T$_{b}$ represent tables in tree structure HTML format. EditDist denotes the tree-edit distance, and | T | represents the number of nodes in T .", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 557.32849, 170.45169, 567.18054], "page": 7, "span": [0, 26], "__ref_s3_data": null}], "text": "5.4. Quantitative Analysis", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.112, 395.86206, 286.36514, 548.3501], "page": 7, "span": [0, 723], "__ref_s3_data": null}], "text": "Structure. As shown in Tab. 2, <PERSON><PERSON><PERSON><PERSON> outperforms all SOTA methods across different datasets by a large margin for predicting the table structure from an image. All the more, our model outperforms pre-trained methods. During the evaluation we do not apply any table filtering. We also provide our baseline results on the SynthTabNet dataset. It has been observed that large tables (e.g. tables that occupy half of the page or more) yield poor predictions. We attribute this issue to the image resizing during the preprocessing step, that produces downsampled images with indistinguishable features. This problem can be addressed by treating such big tables with a separate model which accepts a large input image size.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/3"}, {"prov": [{"bbox": [50.112, 178.**************, 286.36511, 199.56664], "page": 7, "span": [0, 101], "__ref_s3_data": null}], "text": "Table 2: Structure results on PubTabNet (PTN), FinTabNet (FTN), TableBank (TB) and SynthTabNet (STN).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 166.**************, 261.78732, 175.**************], "page": 7, "span": [0, 50], "__ref_s3_data": null}], "text": "FT: Model was trained on PubTabNet then finetuned.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112015, 78.**************, 286.366, 147.**************], "page": 7, "span": [0, 1084], "__ref_s3_data": null}, {"bbox": [50.112015, 78.**************, 286.366, 147.**************], "page": 7, "span": [0, 1084], "__ref_s3_data": null}], "text": "Cell Detection. Like any object detector, our Cell BBox Detector provides bounding boxes that can be improved with post-processing during inference. We make use of the grid-like structure of tables to refine the predictions. A detailed explanation on the post-processing is available in the supplementary material. As shown in Tab. 3, we evaluate our Cell BBox Decoder accuracy for cells with a class label of 'content' only using the PASCAL VOC mAP metric for pre-processing and post-processing. Note that we do not have post-processing results for SynthTabNet as images are only provided. To compare the performance of our proposed approach, we've integrated TableFormer's Cell BBox Decoder into EDD architecture. As mentioned previously, the Structure Decoder provides the Cell BBox Decoder with the features needed to predict the bounding box predictions. Therefore, the accuracy of the Structure Decoder directly influences the accuracy of the Cell BBox Decoder . If the Structure Decoder predicts an extra column, this will result in an extra column of predicted bounding boxes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/4"}, {"prov": [{"bbox": [308.862, 454.6891499999999, 545.11517, 475.55069], "page": 7, "span": [0, 94], "__ref_s3_data": null}], "text": "Table 3: Cell Bounding Box detection results on PubTabNet, and FinTabNet. PP: Post-processing.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [308.86197, 271.83231, 545.11566, 424.3202800000001], "page": 7, "span": [0, 715], "__ref_s3_data": null}], "text": "Cell Content. In this section, we evaluate the entire pipeline of recovering a table with content. Here we put our approach to test by capitalizing on extracting content from the PDF cells rather than decoding from images. Tab. 4 shows the TEDs score of HTML code representing the structure of the table along with the content inserted in the data cell and compared with the ground-truth. Our method achieved a 5.3% increase over the state-of-the-art, and commercial solutions. We believe our scores would be higher if the HTML ground-truth matched the extracted PDF cell content. Unfortunately, there are small discrepancies such as spacings around words or special characters with various unicode representations.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/5"}, {"prov": [{"bbox": [308.862, 102.32206999999994, 545.11517, 135.13864], "page": 7, "span": [0, 148], "__ref_s3_data": null}], "text": "Table 4: Results of structure with content retrieved using cell detection on PubTabNet. In all cases the input is PDF documents with cropped tables.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [53.286037, 705.43927, 61.550289, 713.3124399999999], "page": 8, "span": [0, 2], "__ref_s3_data": null}], "text": "- a.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [65.682419, 705.43927, 499.55563, 713.3124399999999], "page": 8, "span": [0, 105], "__ref_s3_data": null}], "text": "- Red - PDF cells, Green - predicted bounding boxes, Blue - post-processed predictions matched to PDF cells", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [53.811783000000005, 689.8457, 284.34592, 697.71887], "page": 8, "span": [0, 53], "__ref_s3_data": null}], "text": "Japanese language (previously unseen by <PERSON><PERSON><PERSON><PERSON>):", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [304.83081, 689.8457, 431.09119, 697.71887], "page": 8, "span": [0, 29], "__ref_s3_data": null}], "text": "Example table from FinTabNet:", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/6"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/7"}, {"prov": [{"bbox": [53.811783000000005, 575.89355, 385.93451, 583.76672], "page": 8, "span": [0, 79], "__ref_s3_data": null}], "text": "b. Structure predicted by <PERSON><PERSON><PERSON><PERSON>, with superimposed matched PDF cell text:", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/6"}, {"prov": [{"bbox": [380.42731, 493.39716, 549.42175, 499.69574], "page": 8, "span": [0, 53], "__ref_s3_data": null}], "text": "Text is aligned to match original for ease of viewing", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/7"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/8"}, {"prov": [{"bbox": [62.595001, 324.36508, 532.63049, 333.27164], "page": 8, "span": [0, 112], "__ref_s3_data": null}], "text": "Figure 6: An example of TableFormer predictions (bounding boxes and structure) from generated SynthTabNet table.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/9"}, {"prov": [{"bbox": [50.112, 426.35013, 545.11377, 471.12265], "page": 8, "span": [0, 397], "__ref_s3_data": null}], "text": "Figure 5: One of the benefits of TableFormer is that it is language agnostic, as an example, the left part of the illustration demonstrates TableFormer predictions on previously unseen language (Japanese). Additionally, we see that TableFormer is robust to variability in style and content, right side of the illustration shows the example of the TableFormer prediction from the FinTabNet dataset.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/10"}, {"prov": [{"bbox": [50.112, 290.75259, 163.7558, 300.60464], "page": 8, "span": [0, 25], "__ref_s3_data": null}], "text": "5.5. Qualitative Analysis", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [308.862, 290.54337, 460.84848, 301.29108], "page": 8, "span": [0, 27], "__ref_s3_data": null}], "text": "6. Future Work & Conclusion", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.112, 78.84805299999994, 286.36511, 255.12662999999998], "page": 8, "span": [0, 866], "__ref_s3_data": null}], "text": "We showcase several visualizations for the different components of our network on various \"complex\" tables within datasets presented in this work in Fig. 5 and Fig. 6 As it is shown, our model is able to predict bounding boxes for all table cells, even for the empty ones. Additionally, our post-processing techniques can extract the cell content by matching the predicted bounding boxes to the PDF cells based on their overlap and spatial proximity. The left part of Fig. 5 demonstrates also the adaptability of our method to any language, as it can successfully extract Japanese text, although the training set contains only English content. We provide more visualizations including the intermediate steps in the supplementary material. Overall these illustrations justify the versatility of our method across a diverse range of table appearances and content type.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 138.69407999999999, 545.11517, 279.10663], "page": 8, "span": [0, 640], "__ref_s3_data": null}], "text": "In this paper, we presented TableFormer an end-to-end transformer based approach to predict table structures and bounding boxes of cells from an image. This approach enables us to recreate the table structure, and extract the cell content from PDF or OCR by using bounding boxes. Additionally, it provides the versatility required in real-world scenarios when dealing with various types of PDF documents, and languages. Furthermore, our method outperforms all state-of-the-arts with a wide margin. Finally, we introduce \"SynthTabNet\" a challenging synthetically generated dataset that reinforces missing characteristics from other datasets.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 109.15336000000002, 364.40585, 119.90107999999998], "page": 8, "span": [0, 10], "__ref_s3_data": null}], "text": "References", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [313.345, 79.06324800000004, 545.1134, 98.03820799999994], "page": 8, "span": [0, 121], "__ref_s3_data": null}], "text": "- [1] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. End-to-", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [70.030998, 675.22424, 286.36334, 716.11621], "page": 9, "span": [0, 212], "__ref_s3_data": null}], "text": "- end object detection with transformers. In <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, editors, Computer Vision - ECCV 2020 , pages 213-229, Cham, 2020. Springer International Publishing. 5", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.595005, 642.0343, 286.36334, 671.9682599999999], "page": 9, "span": [0, 165], "__ref_s3_data": null}], "text": "- [2] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Complicated table structure recognition. arXiv preprint arXiv:1908.04729 , 2019. 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.595001, 608.84534, 286.36301, 638.77832], "page": 9, "span": [0, 125], "__ref_s3_data": null}], "text": "- [3] <PERSON> and <PERSON><PERSON><PERSON>. Recognition of Tables and Forms , pages 647-677. Springer London, London, 2014. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.59499, 564.69641, 286.36401, 605.58936], "page": 9, "span": [0, 216], "__ref_s3_data": null}], "text": "- [4] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. ICDAR 2019 Competition on Table Detection and Recognition (cTDaR), Apr. 2019. http://sac.founderit.com/. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.594982, 520.54846, 286.36334, 561.44043], "page": 9, "span": [0, 236], "__ref_s3_data": null}], "text": "- [5] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Automatic table detection in document images. In International Conference on Pattern Recognition and Image Analysis , pages 609-618. Springer, 2005. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.594971, 476.3995100000001, 286.36676, 517.29242], "page": 9, "span": [0, 194], "__ref_s3_data": null}], "text": "- [6] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON>. Icdar 2013 table competition. In 2013 12th International Conference on Document Analysis and Recognition , pages 1449-1453, 2013. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.59499, 443.21048, 286.36319, 473.14346], "page": 9, "span": [0, 165], "__ref_s3_data": null}], "text": "- [7] <PERSON> and <PERSON>. Recognition of tables using table grammars. procs. In Symposium on Document Analysis and Recognition (SDAIR'95) , pages 261-277. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.594986000000006, 388.10251, 286.36331, 439.9544399999999], "page": 9, "span": [0, 273], "__ref_s3_data": null}], "text": "- [8] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Castabdetectors: Cascade network for table detection in document images with recursive feature pyramid and switchable atrous convolution. Journal of Imaging , 7(10), 2021. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [54.595001, 354.91353999999995, 286.35989, 384.84746999999993], "page": 9, "span": [0, 170], "__ref_s3_data": null}], "text": "- [9] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Mask r-cnn. In Proceedings of the IEEE International Conference on Computer Vision (ICCV) , Oct 2017. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 310.76456, 286.36334, 351.6575000000001], "page": 9, "span": [0, 226], "__ref_s3_data": null}], "text": "- [10] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Pingan-vcgroup's solution for icdar 2021 competition on scientific table image recognition to latex. ArXiv , abs/2105.01846, 2021. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 255.65761999999995, 286.36331, 307.50952], "page": 9, "span": [0, 239], "__ref_s3_data": null}], "text": "- [11] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, and <PERSON>. Medium-independent table detection. In Document Recognition and Retrieval VII , volume 3967, pages 291-302. International Society for Optics and Photonics, 1999. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 200.55062999999996, 286.36334, 252.40157999999997], "page": 9, "span": [0, 240], "__ref_s3_data": null}], "text": "- [12] <PERSON>. A constraint-based approach to table structure derivation. In Proceedings of the Seventh International Conference on Document Analysis and Recognition - Volume 2 , ICDAR '03, page 911, USA, 2003. IEEE Computer Society. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 145.44263, 286.36331, 197.29458999999997], "page": 9, "span": [0, 283], "__ref_s3_data": null}], "text": "- [13] <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Learning to detect tables in scanned document images using line information. In 2013 12th International Conference on Document Analysis and Recognition , pages 1185-1189. IEEE, 2013. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.111992, 112.25361999999996, 286.36334, 142.18658000000005], "page": 9, "span": [0, 142], "__ref_s3_data": null}], "text": "- [14] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Icdar 2021 competition on scientific table image recognition to latex, 2021. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.111992, 79.06361400000003, 286.35931, 108.99757], "page": 9, "span": [0, 127], "__ref_s3_data": null}], "text": "- [15] <PERSON>. The hungarian method for the assignment problem. Naval research logistics quarterly , 2(1-2):83-97, 1955. 6", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86197, 653.30664, 545.11475, 716.1165799999999], "page": 9, "span": [0, 287], "__ref_s3_data": null}], "text": "- [16] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON>. Babytalk: Understanding and generating simple image descriptions. IEEE Transactions on Pattern Analysis and Machine Intelligence , 35(12):2891-2903, 2013. 4", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 619.94366, 545.1134, 649.87665], "page": 9, "span": [0, 156], "__ref_s3_data": null}], "text": "- [17] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Tablebank: A benchmark dataset for table detection and recognition, 2019. 2, 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 531.78577, 545.11353, 616.51367], "page": 9, "span": [0, 407], "__ref_s3_data": null}], "text": "- [18] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Gfte: Graph-based financial table extraction. In <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>, editors, Pattern Recognition. ICPR International Workshops and Challenges , pages 644-658, Cham, 2021. Springer International Publishing. 2, 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 465.54587, 545.1142, 528.35577], "page": 9, "span": [0, 328], "__ref_s3_data": null}], "text": "- [19] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON>. Robust pdf document conversion using recurrent neural networks. Proceedings of the AAAI Conference on Artificial Intelligence , 35(17):15137-15145, May 2021. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 421.22287, 545.11609, 462.11581], "page": 9, "span": [0, 229], "__ref_s3_data": null}], "text": "- [20] <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>. Parsing table structures in the wild. In Proceedings of the IEEE/CVF International Conference on Computer Vision , pages 944-952, 2021. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 354.98294, 545.11346, 417.79382], "page": 9, "span": [0, 315], "__ref_s3_data": null}], "text": "- [21] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Tablenet: Deep learning model for end-to-end table detection and tabular data extraction from scanned document images. In 2019 International Conference on Document Analysis and Recognition (ICDAR) , pages 128-133. IEEE, 2019. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 233.94903999999997, 545.11475, 351.55389], "page": 9, "span": [0, 592], "__ref_s3_data": null}], "text": "- [22] <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Pytorch: An imperative style, high-performance deep learning library. In <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems 32 , pages 8024-8035. Curran Associates, Inc., 2019. 6", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 167.70902999999998, 545.1134, 230.519], "page": 9, "span": [0, 322], "__ref_s3_data": null}], "text": "- [23] <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Cascadetabnet: An approach for end to end table detection and structure recognition from image-based documents. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition Workshops , pages 572-573, 2020. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 123.38602000000003, 545.11621, 164.27899000000002], "page": 9, "span": [0, 224], "__ref_s3_data": null}], "text": "- [24] <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Rethinking table recognition using graph neural networks. In 2019 International Conference on Document Analysis and Recognition (ICDAR) , pages 142-147. IEEE, 2019. 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86203, 79.06402600000001, 545.1134, 119.95699000000002], "page": 9, "span": [0, 229], "__ref_s3_data": null}], "text": "- [25] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Generalized intersection over union: A metric and a loss for bounding box regression. In Proceedings of the IEEE/CVF Conference on", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [70.030998, 697.14124, 286.36176, 716.11621], "page": 10, "span": [0, 64], "__ref_s3_data": null}], "text": "Computer Vision and Pattern Recognition , pages 658-666, 2019. 6", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112007, 631.02332, 286.36578, 693.83423], "page": 10, "span": [0, 302], "__ref_s3_data": null}], "text": "- [26] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deepdesrt: Deep learning for detection and structure recognition of tables in document images. In 2017 14th IAPR International Conference on Document Analysis and Recognition (ICDAR) , volume 01, pages 11621167, 2017. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 564.9054, 286.36337, 627.71533], "page": 10, "span": [0, 308], "__ref_s3_data": null}], "text": "- [27] <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON>. Deepdesrt: Deep learning for detection and structure recognition of tables in document images. In 2017 14th IAPR international conference on document analysis and recognition (ICDAR) , volume 1, pages 1162-1167. IEEE, 2017. 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 520.70447, 286.36578, 561.59741], "page": 10, "span": [0, 183], "__ref_s3_data": null}], "text": "- [28] <PERSON><PERSON><PERSON> and <PERSON>. Table detection in heterogeneous documents. In Proceedings of the 9th IAPR International Workshop on Document Analysis Systems , pages 6572, 2010. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 465.5455, 286.36627, 517.39642], "page": 10, "span": [0, 275], "__ref_s3_data": null}], "text": "- [29] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Deeptabstr: Deep learning based table structure recognition. In 2019 International Conference on Document Analysis and Recognition (ICDAR) , pages 1403-1409. IEEE, 2019. 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 410.38553, 286.36334, 462.23746], "page": 10, "span": [0, 251], "__ref_s3_data": null}], "text": "- [30] <PERSON>, <PERSON>, <PERSON>, and <PERSON><PERSON>. Corpus conversion service: A machine learning platform to ingest documents at scale. In Proceedings of the 24th ACM SIGKDD , KDD '18, pages 774-782, New York, NY, USA, 2018. ACM. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 333.30856, 286.36389, 407.07748], "page": 10, "span": [0, 366], "__ref_s3_data": null}], "text": "- [31] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>. Attention is all you need. In <PERSON><PERSON>, U. V. <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON><PERSON>, editors, Advances in Neural Information Processing Systems 30 , pages 5998-6008. Curran Associates, Inc., 2017. 5", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112007, 289.10757, 286.36334, 330.00052], "page": 10, "span": [0, 221], "__ref_s3_data": null}], "text": "- [32] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Show and tell: A neural image caption generator. In Proceedings of the IEEE Conference on Computer Vision and Pattern Recognition (CVPR) , June 2015. 2", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112015, 244.90756, 286.36337, 285.79953], "page": 10, "span": [0, 217], "__ref_s3_data": null}], "text": "- [33] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. Res2tim: reconstruct syntactic structures from table images. In 2019 International Conference on Document Analysis and Recognition (ICDAR) , pages 749-755. IEEE, 2019. 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112022, 200.70655999999997, 286.36337, 241.59951999999998], "page": 10, "span": [0, 190], "__ref_s3_data": null}], "text": "- [34] <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. Tgrnet: A table graph reconstruction network for table structure recognition. arXiv preprint arXiv:2106.10598 , 2021. 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.11203, 156.50554999999997, 286.3634, 197.39851], "page": 10, "span": [0, 220], "__ref_s3_data": null}], "text": "- [35] <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>. Image captioning with semantic attention. In Proceedings of the IEEE conference on computer vision and pattern recognition , pages 4651-4659, 2016. 4", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112022, 101.34653000000003, 286.36337, 153.19750999999997], "page": 10, "span": [0, 280], "__ref_s3_data": null}], "text": "- [36] <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, and <PERSON>. Global table extractor (gte): A framework for joint table identification and cell structure recognition using visual context. Winter Conference for Applications in Computer Vision (WACV) , 2021. 2, 3", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112015, 79.063538, 286.36334, 98.038498], "page": 10, "span": [0, 106], "__ref_s3_data": null}], "text": "- [37] <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Image-based table recognition: Data, model,", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [328.78101, 675.22455, 545.11456, 716.11652], "page": 10, "span": [0, 192], "__ref_s3_data": null}], "text": "- and evaluation. In <PERSON>, <PERSON><PERSON>, <PERSON>, and <PERSON><PERSON><PERSON>, editors, Computer Vision ECCV 2020 , pages 564-580, Cham, 2020. Springer International Publishing. 2, 3, 7", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 630.39258, 545.11334, 671.28552], "page": 10, "span": [0, 221], "__ref_s3_data": null}], "text": "- [38] <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. Publaynet: Largest dataset ever for document layout analysis. In 2019 International Conference on Document Analysis and Recognition (ICDAR) , pages 1015-1022, 2019. 1", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [132.842, 656.46991, 465.37591999999995, 681.42511], "page": 11, "span": [0, 83], "__ref_s3_data": null}], "text": "TableFormer: Table Structure Understanding with Transformers Supplementary Material", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111984, 620.09137, 175.96437, 630.83911], "page": 11, "span": [0, 26], "__ref_s3_data": null}], "text": "1. Details on the datasets", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111984, 601.16864, 150.36401, 611.02069], "page": 11, "span": [0, 21], "__ref_s3_data": null}], "text": "1.1. Data preparation", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111984, 403.84517999999997, 286.36514, 592.07971], "page": 11, "span": [0, 931], "__ref_s3_data": null}], "text": "As a first step of our data preparation process, we have calculated statistics over the datasets across the following dimensions: (1) table size measured in the number of rows and columns, (2) complexity of the table, (3) strictness of the provided HTML structure and (4) completeness (i.e. no omitted bounding boxes). A table is considered to be simple if it does not contain row spans or column spans. Additionally, a table has a strict HTML structure if every row has the same number of columns after taking into account any row or column spans. Therefore a strict HTML structure looks always rectangular. However, HTML is a lenient encoding format, i.e. tables with rows of different sizes might still be regarded as correct due to implicit display rules. These implicit rules leave room for ambiguity, which we want to avoid. As such, we prefer to have \"strict\" tables, i.e. tables where every row has exactly the same length.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111984, 164.5403, 286.36511, 400.59473], "page": 11, "span": [0, 1149], "__ref_s3_data": null}], "text": "We have developed a technique that tries to derive a missing bounding box out of its neighbors. As a first step, we use the annotation data to generate the most fine-grained grid that covers the table structure. In case of strict HTML tables, all grid squares are associated with some table cell and in the presence of table spans a cell extends across multiple grid squares. When enough bounding boxes are known for a rectangular table, it is possible to compute the geometrical border lines between the grid rows and columns. Eventually this information is used to generate the missing bounding boxes. Additionally, the existence of unused grid squares indicates that the table rows have unequal number of columns and the overall structure is non-strict. The generation of missing bounding boxes for non-strict HTML tables is ambiguous and therefore quite challenging. Thus, we have decided to simply discard those tables. In case of PubTabNet we have computed missing bounding boxes for 48% of the simple and 69% of the complex tables. Regarding FinTabNet, 68% of the simple and 98% of the complex tables require the generation of bounding boxes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111984, 140.**************, 286.36496, 161.28985999999998], "page": 11, "span": [0, 92], "__ref_s3_data": null}], "text": "Figure 7 illustrates the distribution of the tables across different dimensions per dataset.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.111984, 119.75780999999995, 153.60785, 129.60986000000003], "page": 11, "span": [0, 23], "__ref_s3_data": null}], "text": "1.2. Synthetic datasets", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [50.111984, 77.85229500000003, 286.36505, 110.66886999999997], "page": 11, "span": [0, 389], "__ref_s3_data": null}, {"bbox": [50.111984, 77.85229500000003, 286.36505, 110.66886999999997], "page": 11, "span": [0, 389], "__ref_s3_data": null}], "text": "Aiming to train and evaluate our models in a broader spectrum of table data we have synthesized four types of datasets. Each one contains tables with different appear- ances in regard to their size, structure, style and content. Every synthetic dataset contains 150k examples, summing up to 600k synthetic examples. All datasets are divided into Train, Test and Val splits (80%, 10%, 10%).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 559.90326, 545.11505, 580.76483], "page": 11, "span": [0, 89], "__ref_s3_data": null}], "text": "The process of generating a synthetic dataset can be decomposed into the following steps:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.862, 475.45721, 545.11511, 556.09479], "page": 11, "span": [0, 373], "__ref_s3_data": null}], "text": "- 1. Prepare styling and content templates: The styling templates have been manually designed and organized into groups of scope specific appearances (e.g. financial data, marketing data, etc.) Additionally, we have prepared curated collections of content templates by extracting the most frequently used terms out of non-synthetic datasets (e.g. PubTabNet, FinTabNet, etc.).", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 343.19135, 545.11517, 471.64978], "page": 11, "span": [0, 573], "__ref_s3_data": null}], "text": "- 2. Generate table structures: The structure of each synthetic dataset assumes a horizontal table header which potentially spans over multiple rows and a table body that may contain a combination of row spans and column spans. However, spans are not allowed to cross the header - body boundary. The table structure is described by the parameters: Total number of table rows and columns, number of header rows, type of spans (header only spans, row only spans, column only spans, both row and column spans), maximum span size and the ratio of the table area covered by spans.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 294.61139, 545.11517, 339.38391], "page": 11, "span": [0, 195], "__ref_s3_data": null}], "text": "- 3. Generate content: Based on the dataset theme , a set of suitable content templates is chosen first. Then, this content can be combined with purely random text to produce the synthetic content.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 246.03142000000003, 545.1153, 290.80396], "page": 11, "span": [0, 218], "__ref_s3_data": null}], "text": "- 4. Apply styling templates: Depending on the domain of the synthetic dataset, a set of styling templates is first manually selected. Then, a style is randomly selected to format the appearance of the synthesized table.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 185.49640999999997, 545.11517, 242.22397], "page": 11, "span": [0, 238], "__ref_s3_data": null}], "text": "- 5. Render the complete tables: The synthetic table is finally rendered by a web browser engine to generate the bounding boxes for each table cell. A batching technique is utilized to optimize the runtime overhead of the rendering process.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 145.01369, 545.10876, 169.70940999999993], "page": 11, "span": [0, 47], "__ref_s3_data": null}], "text": "2. Prediction post-processing for PDF documents", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [308.86203, 77.85139500000002, 545.11517, 134.57896000000005], "page": 11, "span": [0, 247], "__ref_s3_data": null}], "text": "Although TableFormer can predict the table structure and the bounding boxes for tables recognized inside PDF documents, this is not enough when a full reconstruction of the original table is required. This happens mainly due the following reasons:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/11"}, {"prov": [{"bbox": [50.112, 605.63605, 545.11371, 626.49762], "page": 12, "span": [0, 245], "__ref_s3_data": null}], "text": "Figure 7: Distribution of the tables across different dimensions per dataset. Simple vs complex tables per dataset and split, strict vs non strict html structures per dataset and table complexity, missing bboxes per dataset and table complexity.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [61.569, 560.20703, 286.36511, 581.0686], "page": 12, "span": [0, 61], "__ref_s3_data": null}], "text": "- · TableFormer output does not include the table cell content.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [61.569, 527.06702, 286.36514, 547.92859], "page": 12, "span": [0, 77], "__ref_s3_data": null}], "text": "- · There are occasional inaccuracies in the predictions of the bounding boxes.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 536.2962, 545.11517, 581.06879], "page": 12, "span": [0, 183], "__ref_s3_data": null}], "text": "dian cell size for all table cells. The usage of median during the computations, helps to eliminate outliers caused by occasional column spans which are usually wider than the normal.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 396.29312, 286.36511, 512.79657], "page": 12, "span": [0, 545], "__ref_s3_data": null}], "text": "However, it is possible to mitigate those limitations by combining the TableFormer predictions with the information already present inside a programmatic PDF document. More specifically, PDF documents can be seen as a sequence of PDF cells where each cell is described by its content and bounding box. If we are able to associate the PDF cells with the predicted table cells, we can directly link the PDF cell content to the table cell structure and use the PDF bounding boxes to correct misalignments in the predicted table cell bounding boxes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 372.06812, 286.36496, 392.93066], "page": 12, "span": [0, 68], "__ref_s3_data": null}], "text": "Here is a step-by-step description of the prediction postprocessing:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 335.88814999999994, 286.36508, 368.70468], "page": 12, "span": [0, 173], "__ref_s3_data": null}], "text": "- 1. Get the minimal grid dimensions - number of rows and columns for the predicted table structure. This represents the most granular grid for the underlying table structure.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 287.7532, 286.36505, 332.52472], "page": 12, "span": [0, 187], "__ref_s3_data": null}], "text": "- 2. Generate pair-wise matches between the bounding boxes of the PDF cells and the predicted cells. The Intersection Over Union (IOU) metric is used to evaluate the quality of the matches.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 263.52721999999994, 286.36493, 284.38977], "page": 12, "span": [0, 97], "__ref_s3_data": null}], "text": "- 3. Use a carefully selected IOU threshold to designate the matches as \"good\" ones and \"bad\" ones.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 227.34722999999997, 286.36511, 260.16479000000004], "page": 12, "span": [0, 131], "__ref_s3_data": null}], "text": "- 3.a. If all IOU scores in a column are below the threshold, discard all predictions (structure and bounding boxes) for that column.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 191.16722000000004, 286.36508, 223.98378000000002], "page": 12, "span": [0, 169], "__ref_s3_data": null}], "text": "- 4. Find the best-fitting content alignment for the predicted cells with good IOU per each column. The alignment of the column can be identified by the following formula:", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [110.70499, 137.89438999999993, 286.3624, 168.56408999999996], "page": 12, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "equation", "payload": null, "name": "Formula", "font": null}, {"prov": [{"bbox": [50.112, 103.07321000000002, 286.362, 124.65208000000007], "page": 12, "span": [0, 103], "__ref_s3_data": null}], "text": "where c is one of { left, centroid, right } and x$_{c}$ is the xcoordinate for the corresponding point.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 78.84821299999999, 286.36496, 99.70977800000003], "page": 12, "span": [0, 110], "__ref_s3_data": null}], "text": "- 5. Use the alignment computed in step 4, to compute the median x -coordinate for all table columns and the me-", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.862, 512.03619, 545.11499, 532.89777], "page": 12, "span": [0, 91], "__ref_s3_data": null}], "text": "- 6. Snap all cells with bad IOU to their corresponding median x -coordinates and cell sizes.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86203, 404.08929, 545.11511, 508.63674999999995], "page": 12, "span": [0, 471], "__ref_s3_data": null}], "text": "- 7. Generate a new set of pair-wise matches between the corrected bounding boxes and PDF cells. This time use a modified version of the IOU metric, where the area of the intersection between the predicted and PDF cells is divided by the PDF cell area. In case there are multiple matches for the same PDF cell, the prediction with the higher score is preferred. This covers the cases where the PDF cells are smaller than the area of predicted or corrected prediction cells.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86203, 332.00836, 545.11517, 400.68985], "page": 12, "span": [0, 311], "__ref_s3_data": null}], "text": "- 8. In some rare occasions, we have noticed that TableFormer can confuse a single column as two. When the postprocessing steps are applied, this results with two predicted columns pointing to the same PDF column. In such case we must de-duplicate the columns according to highest total column intersection score.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86203, 224.06142, 545.11517, 328.60892], "page": 12, "span": [0, 503], "__ref_s3_data": null}], "text": "- 9. Pick up the remaining orphan cells. There could be cases, when after applying all the previous post-processing steps, some PDF cells could still remain without any match to predicted cells. However, it is still possible to deduce the correct matching for an orphan PDF cell by mapping its bounding box on the geometry of the grid. This mapping decides if the content of the orphan cell will be appended to an already matched table cell, or a new table cell should be created to match with the orphan.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86203, 187.84541000000002, 545.11688, 220.66197], "page": 12, "span": [0, 113], "__ref_s3_data": null}], "text": "9a. Compute the top and bottom boundary of the horizontal band for each grid row (min/max y coordinates per row).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [308.86206, 163.58440999999993, 545.11505, 184.44696], "page": 12, "span": [0, 101], "__ref_s3_data": null}], "text": "- 9b. Intersect the orphan's bounding box with the row bands, and map the cell to the closest grid row.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86206, 127.36941000000002, 545.11505, 160.18597], "page": 12, "span": [0, 117], "__ref_s3_data": null}], "text": "- 9c. Compute the left and right boundary of the vertical band for each grid column (min/max x coordinates per column).", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86206, 103.10841000000005, 545.11499, 123.96996999999999], "page": 12, "span": [0, 107], "__ref_s3_data": null}], "text": "- 9d. Intersect the orphan's bounding box with the column bands, and map the cell to the closest grid column.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [308.86206, 78.84840399999996, 545.11517, 99.70997599999998], "page": 12, "span": [0, 118], "__ref_s3_data": null}], "text": "- 9e. If the table cell under the identified row and column is not empty, extend its content with the content of the or-", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [50.112, 707.88507, 88.846588, 716.79163], "page": 13, "span": [0, 10], "__ref_s3_data": null}], "text": "phan cell.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 683.*************, 286.36496, 704.83661], "page": 13, "span": [0, 76], "__ref_s3_data": null}], "text": "9f. Otherwise create a new structural cell and match it wit the orphan cell.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [50.112, 660.29413, 286.36499, 680.83691], "page": 13, "span": [0, 97], "__ref_s3_data": null}], "text": "Aditional images with examples of TableFormer predictions and post-processing can be found below.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/8"}, {"name": "Table", "type": "table", "$ref": "#/tables/9"}, {"name": "Table", "type": "table", "$ref": "#/tables/10"}, {"name": "Table", "type": "table", "$ref": "#/tables/11"}, {"prov": [{"bbox": [63.341, 281.03708, 273.13342, 289.94363], "page": 13, "span": [0, 52], "__ref_s3_data": null}], "text": "Figure 8: Example of a table with multi-line header.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/12"}, {"prov": [{"bbox": [308.862, 464.54009999999994, 545.11511, 485.40164], "page": 13, "span": [0, 67], "__ref_s3_data": null}], "text": "Figure 9: Example of a table with big empty distance between cells.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/13"}, {"name": "Table", "type": "table", "$ref": "#/tables/14"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/12"}, {"name": "Table", "type": "table", "$ref": "#/tables/15"}, {"name": "Table", "type": "table", "$ref": "#/tables/16"}, {"prov": [{"bbox": [312.34299, 102.60006999999996, 541.63232, 111.50664000000006], "page": 13, "span": [0, 55], "__ref_s3_data": null}], "text": "Figure 10: Example of a complex table with empty cells.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/17"}, {"name": "Table", "type": "table", "$ref": "#/tables/18"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/13"}, {"name": "Table", "type": "table", "$ref": "#/tables/19"}, {"name": "Table", "type": "table", "$ref": "#/tables/20"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/14"}, {"prov": [{"bbox": [50.112, 414.3681, 286.36508, 435.22964], "page": 14, "span": [0, 61], "__ref_s3_data": null}], "text": "Figure 11: Simple table with different style and empty cells.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/21"}, {"name": "Table", "type": "table", "$ref": "#/tables/22"}, {"name": "Table", "type": "table", "$ref": "#/tables/23"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/15"}, {"prov": [{"bbox": [54.618998999999995, 111.27508, 281.8559, 120.18164000000002], "page": 14, "span": [0, 56], "__ref_s3_data": null}], "text": "Figure 12: Simple table predictions and post processing.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/24"}, {"name": "Table", "type": "table", "$ref": "#/tables/25"}, {"name": "Table", "type": "table", "$ref": "#/tables/26"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/16"}, {"prov": [{"bbox": [315.79001, 411.40909, 538.18524, 420.31564], "page": 14, "span": [0, 55], "__ref_s3_data": null}], "text": "Figure 13: Table predictions example on colorful table.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/27"}, {"prov": [{"bbox": [344.98499, 99.54707299999995, 508.98935000000006, 108.45363999999995], "page": 14, "span": [0, 40], "__ref_s3_data": null}], "text": "Figure 14: Example with multi-line text.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/28"}, {"name": "Table", "type": "table", "$ref": "#/tables/29"}, {"name": "Table", "type": "table", "$ref": "#/tables/30"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/17"}, {"name": "Table", "type": "table", "$ref": "#/tables/31"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/18"}, {"name": "Table", "type": "table", "$ref": "#/tables/32"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/19"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/20"}, {"name": "Table", "type": "table", "$ref": "#/tables/33"}, {"prov": [{"bbox": [84.233002, 138.74207, 252.24225, 147.64861999999994], "page": 15, "span": [0, 41], "__ref_s3_data": null}], "text": "Figure 15: Example with triangular table.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/34"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/21"}, {"name": "Table", "type": "table", "$ref": "#/tables/35"}, {"name": "Picture", "type": "figure", "$ref": "#/figures/22"}, {"name": "Table", "type": "table", "$ref": "#/tables/36"}, {"name": "Table", "type": "table", "$ref": "#/tables/37"}, {"prov": [{"bbox": [308.86197, 118.20308999999997, 545.11511, 139.06465000000003], "page": 15, "span": [0, 106], "__ref_s3_data": null}], "text": "Figure 16: Example of how post-processing helps to restore mis-aligned bounding boxes prediction artifact.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/23"}, {"prov": [{"bbox": [50.112, 262.80108999999993, 545.11383, 283.66263], "page": 16, "span": [0, 153], "__ref_s3_data": null}], "text": "Figure 17: Example of long table. End-to-end example from initial PDF cells to prediction of bounding boxes, post processing and prediction of structure.", "type": "caption", "payload": null, "name": "Caption", "font": null}], "figures": [{"prov": [{"bbox": [315.65362548828125, 489.1985778808594, 537.1475219726562, 563.276611328125], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [314.78173828125, 381.9505615234375, 539.1802978515625, 453.9347229003906], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [315.7172546386719, 295.9709777832031, 536.835693359375, 358.176513671875], "page": 1, "span": [0, 220], "__ref_s3_data": null}], "text": "Figure 1: Picture of a table with subtle, complex features such as (1) multi-column headers, (2) cell with multi-row text and (3) cells with no content. Image from PubTabNet evaluation set, filename: 'PMC2944238 004 02'.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [312.10369873046875, 541.3901519775391, 550.***********, 713.5591354370117], "page": 3, "span": [0, 104], "__ref_s3_data": null}], "text": "Figure 2: Distribution of the tables across different table dimensions in PubTabNet + FinTabNet datasets", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [74.30525970458984, 608.2984924316406, 519.9801025390625, 714.0887985229492], "page": 5, "span": [0, 212], "__ref_s3_data": null}], "text": "Figure 3: <PERSON><PERSON><PERSON><PERSON> takes in an image of the PDF and creates bounding box and HTML structure predictions that are synchronized. The bounding boxes grabs the content from the PDF and inserts it in the structure.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [53.03328323364258, 284.3311462402344, 285.3731689453125, 534.3346557617188], "page": 5, "span": [0, 745], "__ref_s3_data": null}], "text": "Figure 4: Given an input image of a table, the Encoder produces fixed-length features that represent the input image. The features are then passed to both the Structure Decoder and Cell BBox Decoder . During training, the Structure Decoder receives 'tokenized tags' of the HTML code that represent the table structure. Afterwards, a transformer encoder and decoder architecture is employed to produce features that are received by a linear layer, and the Cell BBox Decoder. The linear layer is applied to the features to predict the tags. Simultaneously, the Cell BBox Decoder selects features referring to the data cells (' < td > ', ' < ') and passes them through an attention network, an MLP, and a linear layer to predict the bounding boxes.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [49.97503662109375, 604.4210662841797, 301.6335754394531, 688.2873382568359], "page": 8, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [305.5836486816406, 611.3732452392578, 554.8258666992188, 693.3458404541016], "page": 8, "span": [0, 79], "__ref_s3_data": null}], "text": "b. Structure predicted by <PERSON><PERSON><PERSON><PERSON>, with superimposed matched PDF cell text:", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [51.736167907714844, 348.3419189453125, 211.83778381347656, 411.51934814453125], "page": 8, "span": [0, 112], "__ref_s3_data": null}], "text": "Figure 6: An example of TableFormer predictions (bounding boxes and structure) from generated SynthTabNet table.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [216.76925659179688, 348.65301513671875, 375.7829284667969, 411.5093688964844], "page": 8, "span": [0, 397], "__ref_s3_data": null}], "text": "Figure 5: One of the benefits of TableFormer is that it is language agnostic, as an example, the left part of the illustration demonstrates TableFormer predictions on previously unseen language (Japanese). Additionally, we see that TableFormer is robust to variability in style and content, right side of the illustration shows the example of the TableFormer prediction from the FinTabNet dataset.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [383.1364440917969, 349.2250671386719, 542.1132202148438, 410.7686767578125], "page": 8, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [53.54227066040039, 644.4090881347656, 544.938232421875, 717.2514572143555], "page": 12, "span": [0, 245], "__ref_s3_data": null}], "text": "Figure 7: Distribution of the tables across different dimensions per dataset. Simple vs complex tables per dataset and split, strict vs non strict html structures per dataset and table complexity, missing bboxes per dataset and table complexity.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [309.***********, 499.60601806640625, 425.9603271484375, 538.0946350097656], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [333.9573669433594, 126.5096435546875, 518.4768676757812, 198.8865966796875], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [51.15378952026367, 447.09332275390625, 282.8598937988281, 687.6914825439453], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [50.40477752685547, 135.83905029296875, 177.0564422607422, 180.99615478515625], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [318.6332092285156, 432.9424133300781, 534.73583984375, 701.1157684326172], "page": 14, "span": [0, 55], "__ref_s3_data": null}], "text": "Figure 13: Table predictions example on colorful table.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [55.116363525390625, 542.6654510498047, 279.370849609375, 655.7449951171875], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [54.28135299682617, 418.4729309082031, 279.2568359375, 531.7384338378906], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [55.423954010009766, 294.436279296875, 280.2310791015625, 407.4449462890625], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [50.64818572998047, 160.736328125, 319.9103088378906, 286.01953125], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [323.46868896484375, 327.739501953125, 525.9569091796875, 429.5491638183594], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [353.6920471191406, 156.22674560546875, 495.4288024902344, 304.594970703125], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [66.79948425292969, 293.8616027832031, 528.5565795898438, 538.3836822509766], "page": 16, "span": [0, 153], "__ref_s3_data": null}], "text": "Figure 17: Example of long table. End-to-end example from initial PDF cells to prediction of bounding boxes, post processing and prediction of structure.", "type": "figure", "payload": null, "bounding-box": null}], "tables": [{"prov": [{"bbox": [315.65362548828125, 489.1985778808594, 537.1475219726562, 563.276611328125], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 1, "#-rows": 1, "data": [[{"bbox": [451.9457100000001, 235.34704999999997, 457.95050000000003, 245.47748], "spans": [[0, 0]], "text": "1", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [315.7172546386719, 295.9709777832031, 536.835693359375, 358.176513671875], "page": 1, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 5, "#-rows": 5, "data": [[{"bbox": [318.88071, 437.68588, 323.27319, 446.47083], "spans": [[0, 0]], "text": "0", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [347.24872, 437.68588, 351.6412, 446.47083], "spans": [[0, 1], [0, 2], [0, 3], [0, 4]], "text": "1 2 1", "type": "body", "col": 1, "col-header": false, "col-span": [1, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [347.24872, 437.68588, 351.6412, 446.47083], "spans": [[0, 1], [0, 2], [0, 3], [0, 4]], "text": "1 2 1", "type": "body", "col": 2, "col-header": false, "col-span": [1, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [347.24872, 437.68588, 351.6412, 446.47083], "spans": [[0, 1], [0, 2], [0, 3], [0, 4]], "text": "1 2 1", "type": "body", "col": 3, "col-header": false, "col-span": [1, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [347.24872, 437.68588, 351.6412, 446.47083], "spans": [[0, 1], [0, 2], [0, 3], [0, 4]], "text": "1 2 1", "type": "body", "col": 4, "col-header": false, "col-span": [1, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [318.77316, 449.5455, 323.16565, 458.33044], "spans": [[1, 0]], "text": "3", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [347.24872, 449.12082, 372.70581, 459.25122], "spans": [[1, 1]], "text": "4 3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [394.10422, 449.5455, 398.4967, 458.33044], "spans": [[1, 2]], "text": "5", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [440.95941000000005, 449.5455, 445.3519, 458.33044], "spans": [[1, 3]], "text": "6", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [487.81491, 449.5455, 492.2074, 458.33044], "spans": [[1, 4]], "text": "7", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [318.77316, 473.70425, 323.16565, 482.4892], "spans": [[2, 0]], "text": "8 2", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [347.24872, 461.8446, 351.6412, 470.62955], "spans": [[2, 1]], "text": "9", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [394.10422, 461.8446, 402.88831, 470.62955], "spans": [[2, 2]], "text": "10", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [440.95941000000005, 461.8446, 449.42285, 470.62955], "spans": [[2, 3]], "text": "11", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [487.81491, 461.8446, 496.599, 470.62955], "spans": [[2, 4]], "text": "12", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [347.24872, 473.70425, 356.03281, 482.4892], "spans": [[3, 0]], "text": "13", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": null, "spans": [[3, 1]], "text": "", "type": "body"}, {"bbox": [394.10422, 473.70425, 402.88831, 482.4892], "spans": [[3, 2]], "text": "14", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [440.95941000000005, 473.70425, 449.7435, 482.4892], "spans": [[3, 3]], "text": "15", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [487.81491, 473.70425, 496.599, 482.4892], "spans": [[3, 4]], "text": "16", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [347.24872, 485.12469, 356.03281, 493.90964], "spans": [[4, 0]], "text": "17", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [394.10422, 485.12469, 402.88831, 493.90964], "spans": [[4, 1]], "text": "18", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": null, "spans": [[4, 2]], "text": "", "type": "body"}, {"bbox": [440.95941000000005, 485.12469, 449.7435, 493.90964], "spans": [[4, 3]], "text": "19", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [487.81491, 485.12469, 496.599, 493.90964], "spans": [[4, 4]], "text": "20", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [310.**************, 636.*************, 542.*************, 718.*************], "page": 4, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 1: Both \"Combined-Tabnet\" and \"CombinedTabnet\" are variations of the following: (*) The CombinedTabnet dataset is the processed combination of PubTabNet and Fintabnet. (**) The combined dataset is the processed combination of PubTabNet, Fintabnet and TableBank.", "type": "table", "payload": null, "#-cols": 5, "#-rows": 7, "data": [[{"bbox": null, "spans": [[0, 0]], "text": "", "type": "body"}, {"bbox": [412.332, 73.**************, 430.90231, 82.52094], "spans": [[0, 1]], "text": "Tags", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [442.85742, 73.**************, 464.*************, 82.52094], "spans": [[0, 2]], "text": "Bbox", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [477.78632, 73.**************, 494.94193, 82.52094], "spans": [[0, 3]], "text": "Size", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [508.28186, 73.**************, 536.91437, 82.52094], "spans": [[0, 4]], "text": "Format", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [317.06, 85.9673499999999, 361.64264, 94.87390000000005], "spans": [[1, 0]], "text": "PubTabNet", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [417.85599, 85.6684600000001, 425.37775, 94.88385000000017], "spans": [[1, 1]], "text": "3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [449.89569, 85.6684600000001, 457.41745000000003, 94.88385000000017], "spans": [[1, 2]], "text": "3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [476.401, 85.9673499999999, 496.3262, 94.87390000000005], "spans": [[1, 3]], "text": "509k", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [512.63495, 85.9673499999999, 532.56012, 94.87390000000005], "spans": [[1, 4]], "text": "PNG", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [317.06, 97.**************, 359.43094, 106.**************], "spans": [[2, 0]], "text": "FinTabNet", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [417.85599, 97.62347, 425.37775, 106.83887000000016], "spans": [[2, 1]], "text": "3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [449.89569, 97.62347, 457.41745000000003, 106.83887000000016], "spans": [[2, 2]], "text": "3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [476.401, 97.**************, 496.3262, 106.**************], "spans": [[2, 3]], "text": "112k", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [513.46185, 97.**************, 531.73328, 106.**************], "spans": [[2, 4]], "text": "PDF", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [317.06, 109.**************, 359.97888, 118.**************], "spans": [[3, 0]], "text": "TableBank", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [417.85599, 109.**************, 425.37775, 118.**************], "spans": [[3, 1]], "text": "3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [450.81226, 109.**************, 456.**************, 118.**************], "spans": [[3, 2]], "text": "7", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [476.401, 109.**************, 496.3262, 118.**************], "spans": [[3, 3]], "text": "145k", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [511.**************, 109.**************, 533.94501, 118.**************], "spans": [[3, 4]], "text": "JPEG", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [317.06, 121.83336999999995, 400.37723, 130.73992999999996], "spans": [[4, 0]], "text": "Combined-Tabnet(*)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [417.85599, 121.53448000000003, 425.37775, 130.74987999999996], "spans": [[4, 1]], "text": "3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [449.89569, 121.53448000000003, 457.41745000000003, 130.74987999999996], "spans": [[4, 2]], "text": "3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [476.401, 121.83336999999995, 496.3262, 130.73992999999996], "spans": [[4, 3]], "text": "400k", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [512.63495, 121.83336999999995, 532.56012, 130.73992999999996], "spans": [[4, 4]], "text": "PNG", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [317.06, 133.78839000000005, 375.17184, 142.69494999999995], "spans": [[5, 0]], "text": "Combined(**)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [417.85599, 133.48950000000002, 425.37775, 142.70489999999995], "spans": [[5, 1]], "text": "3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [449.89569, 133.48950000000002, 457.41745000000003, 142.70489999999995], "spans": [[5, 2]], "text": "3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [476.401, 133.78839000000005, 496.3262, 142.69494999999995], "spans": [[5, 3]], "text": "500k", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [512.63495, 133.78839000000005, 532.56012, 142.69494999999995], "spans": [[5, 4]], "text": "PNG", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [317.06, 145.74341000000004, 369.39352, 154.64995999999996], "spans": [[6, 0]], "text": "SynthTabNet", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [417.85599, 145.44446000000005, 425.37775, 154.65985], "spans": [[6, 1]], "text": "3", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [449.89569, 145.44446000000005, 457.41745000000003, 154.65985], "spans": [[6, 2]], "text": "3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [476.401, 145.74334999999996, 496.3262, 154.6499], "spans": [[6, 3]], "text": "600k", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [512.63495, 145.74334999999996, 532.56012, 154.6499], "spans": [[6, 4]], "text": "PNG", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [53.368526458740234, 209.60223388671875, 283.0443420410156, 382.8642272949219], "page": 7, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 5, "#-rows": 11, "data": [[{"bbox": [78.843002, 420.69037, 104.85535, 429.59692], "spans": [[0, 0]], "text": "Model", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [129.338, 426.66736, 159.21584, 435.57391000000007], "spans": [[0, 1]], "text": "Dataset", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [171.17096, 426.66736, 199.40497, 435.57391000000007], "spans": [[0, 2]], "text": "Simple", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [211.2, 414.71237, 236.10649, 423.61893], "spans": [[0, 3]], "text": "TEDS Complex", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [264.54044, 426.66736, 277.27264, 435.57391000000007], "spans": [[0, 4]], "text": "All", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [81.612, 443.62436, 102.08514, 452.53091], "spans": [[1, 0]], "text": "EDD", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [134.87206, 443.62436, 153.69141, 452.53091], "spans": [[1, 1]], "text": "PTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [176.56554, 443.62436, 194.00009, 452.53091], "spans": [[1, 2]], "text": "91.1", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [220.82938000000001, 443.62436, 238.26393, 452.53091], "spans": [[1, 3]], "text": "88.7", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [262.18414, 443.62436, 279.61868, 452.53091], "spans": [[1, 4]], "text": "89.9", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [82.165001, 455.58035, 101.5323, 464.48691], "spans": [[2, 0]], "text": "GTE", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [134.86716, 455.58035, 153.68651, 464.48691], "spans": [[2, 1]], "text": "PTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [183.62411, 455.58035, 186.94167, 464.48691], "spans": [[2, 2]], "text": "-", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [227.88795000000002, 455.58035, 231.20551, 464.48691], "spans": [[2, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [259.69855, 455.58035, 282.11441, 464.48691], "spans": [[2, 4]], "text": "93.01", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [66.315002, 468.13336, 117.38329000000002, 477.03992], "spans": [[3, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [134.86766, 468.13336, 153.68701, 477.03992], "spans": [[3, 1]], "text": "PTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [176.57111, 468.13336, 194.00566, 477.03992], "spans": [[3, 2]], "text": "98.5", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [220.83495, 468.13336, 238.26950000000002, 477.03992], "spans": [[3, 3]], "text": "95.0", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [259.698, 468.01379, 282.11386, 476.97018], "spans": [[3, 4]], "text": "96.75", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [81.612, 483.32635, 102.08514, 492.23291], "spans": [[4, 0]], "text": "EDD", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [134.87206, 483.32635, 153.69141, 492.23291], "spans": [[4, 1]], "text": "FTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [176.56554, 483.32635, 194.00009, 492.23291], "spans": [[4, 2]], "text": "88.4", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [218.33870999999996, 483.32635, 240.75455999999997, 492.23291], "spans": [[4, 3]], "text": "92.08", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [262.18411, 483.32635, 279.61865, 492.23291], "spans": [[4, 4]], "text": "90.6", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [82.165001, 495.28134, 101.5323, 504.1879], "spans": [[5, 0]], "text": "GTE", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [134.86716, 495.28134, 153.68651, 504.1879], "spans": [[5, 1]], "text": "FTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [183.62411, 495.28134, 186.94167, 504.1879], "spans": [[5, 2]], "text": "-", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [227.88795000000002, 495.28134, 231.20551, 504.1879], "spans": [[5, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [259.69855, 495.28134, 282.11441, 504.1879], "spans": [[5, 4]], "text": "87.14", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [71.789001, 507.23633, 111.90838999999998, 516.14288], "spans": [[6, 0]], "text": "GTE (FT)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [134.86221, 507.23633, 153.68156, 516.14288], "spans": [[6, 1]], "text": "FTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [183.62914, 507.23633, 186.94669, 516.14288], "spans": [[6, 2]], "text": "-", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [227.89297, 507.23633, 231.21053000000003, 516.14288], "spans": [[6, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [259.6936, 507.23633, 282.10947, 516.14288], "spans": [[6, 4]], "text": "91.02", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [66.315002, 519.1913099999999, 117.38329000000002, 528.0978700000001], "spans": [[7, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [134.86766, 519.1913099999999, 153.68701, 528.0978700000001], "spans": [[7, 1]], "text": "FTN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [176.57111, 519.1913099999999, 194.00566, 528.0978700000001], "spans": [[7, 2]], "text": "97.5", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [220.83495, 519.1913099999999, 238.26950000000002, 528.0978700000001], "spans": [[7, 3]], "text": "96.0", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [262.189, 519.0717500000001, 279.62354, 528.02814], "spans": [[7, 4]], "text": "96.8", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [81.612, 536.49837, 102.08514, 545.40492], "spans": [[8, 0]], "text": "EDD", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [137.91064, 536.49837, 150.64285, 545.40492], "spans": [[8, 1]], "text": "TB", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [176.56554, 536.49837, 194.00009, 545.40492], "spans": [[8, 2]], "text": "86.0", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [227.89285, 536.49837, 231.21040000000002, 545.40492], "spans": [[8, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [262.18411, 536.49837, 279.61865, 545.40492], "spans": [[8, 4]], "text": "86.0", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [66.315002, 548.45436, 117.38329000000002, 557.36092], "spans": [[9, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [137.90625, 548.45436, 150.63846, 557.36092], "spans": [[9, 1]], "text": "TB", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [176.57111, 548.45436, 194.00566, 557.36092], "spans": [[9, 2]], "text": "89.6", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [227.88845999999998, 548.45436, 231.20601, 557.36092], "spans": [[9, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [262.189, 548.3348100000001, 279.62354, 557.2911799999999], "spans": [[9, 4]], "text": "89.6", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}], [{"bbox": [66.315002, 568.00237, 117.38329000000002, 576.90892], "spans": [[10, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 10, "row-header": true, "row-span": [10, 11]}, {"bbox": [134.86766, 568.00237, 153.68701, 576.90892], "spans": [[10, 1]], "text": "STN", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [176.57111, 568.00237, 194.00566, 576.90892], "spans": [[10, 2]], "text": "96.9", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [220.83495, 568.00237, 238.26950000000002, 576.90892], "spans": [[10, 3]], "text": "95.7", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 10, "row-header": false, "row-span": [10, 11]}, {"bbox": [262.1897, 568.00237, 279.62424, 576.90892], "spans": [[10, 4]], "text": "96.7", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 10, "row-header": false, "row-span": [10, 11]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [308.4068603515625, 488.1943359375, 533.6419677734375, 544.1236877441406], "page": 7, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 3: Cell Bounding Box detection results on PubTabNet, and FinTabNet. PP: Post-processing.", "type": "table", "payload": null, "#-cols": 4, "#-rows": 4, "data": [[{"bbox": [339.323, 253.66436999999996, 365.33536, 262.57092], "spans": [[0, 0]], "text": "Model", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [401.04132, 253.66436999999996, 430.91916, 262.57092], "spans": [[0, 1]], "text": "Dataset", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [454.10214, 253.66436999999996, 474.58523999999994, 262.57092], "spans": [[0, 2]], "text": "mAP", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [486.54034, 253.66436999999996, 527.2276, 262.57092], "spans": [[0, 3]], "text": "mAP (PP)", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [327.65601, 270.62134000000003, 377.00076, 279.52788999999996], "spans": [[1, 0]], "text": "EDD+BBox", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [393.69809, 270.62134000000003, 438.28073, 279.52788999999996], "spans": [[1, 1]], "text": "PubTabNet", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [455.63559, 270.62134000000003, 473.07013, 279.52788999999996], "spans": [[1, 2]], "text": "79.2", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [498.16592, 270.62134000000003, 515.60046, 279.52788999999996], "spans": [[1, 3]], "text": "82.7", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [326.79501, 282.57631999999995, 377.86331, 291.48288], "spans": [[2, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [393.69388, 282.57631999999995, 438.27652, 291.48288], "spans": [[2, 1]], "text": "PubTabNet", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [455.63101, 282.45676, 473.06555000000003, 291.41315], "spans": [[2, 2]], "text": "82.1", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [498.1713, 282.45676, 515.60583, 291.41315], "spans": [[2, 3]], "text": "86.8", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [326.79501, 294.53131, 377.86331, 303.43787], "spans": [[3, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [389.81842, 294.53131, 442.15194999999994, 303.43787], "spans": [[3, 1]], "text": "SynthTabNet", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [455.63135, 294.53131, 473.06589, 303.43787], "spans": [[3, 2]], "text": "87.7", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [505.22515999999996, 294.53131, 508.54268999999994, 303.43787], "spans": [[3, 3]], "text": "-", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [332.9688720703125, 148.73028564453125, 520.942138671875, 251.7164306640625], "page": 7, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 4: Results of structure with content retrieved using cell detection on PubTabNet. In all cases the input is PDF documents with cropped tables.", "type": "table", "payload": null, "#-cols": 4, "#-rows": 7, "data": [[{"bbox": [358.01099, 552.23337, 384.02335, 561.1399200000001], "spans": [[0, 0]], "text": "Model", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [408.50598, 558.21037, 436.73999, 567.11693], "spans": [[0, 1]], "text": "Simple", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [449.03400000000005, 546.25537, 473.94049000000007, 555.16193], "spans": [[0, 2]], "text": "TEDS Complex", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [499.3848, 558.21037, 512.117, 567.11693], "spans": [[0, 3]], "text": "All", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [357.68201, 575.16736, 384.3519, 584.0739100000001], "spans": [[1, 0]], "text": "Tabula", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 1, "row-header": true, "row-span": [1, 2]}, {"bbox": [413.90097, 575.16736, 431.33550999999994, 584.0739100000001], "spans": [[1, 1]], "text": "78.0", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [458.16479000000004, 575.16736, 475.59933000000007, 584.0739100000001], "spans": [[1, 2]], "text": "57.8", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [497.0289, 575.16736, 514.46344, 584.0739100000001], "spans": [[1, 3]], "text": "67.9", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [350.72299, 587.12236, 391.31064, 596.02892], "spans": [[2, 0]], "text": "Traprange", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [413.90582, 587.12236, 431.34036, 596.02892], "spans": [[2, 1]], "text": "60.8", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [458.16965, 587.12236, 475.60419, 596.02892], "spans": [[2, 2]], "text": "49.9", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [497.03374999999994, 587.12236, 514.46832, 596.02892], "spans": [[2, 3]], "text": "55.4", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [354.13599, 599.07835, 387.89923, 607.98491], "spans": [[3, 0]], "text": "<PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [413.90161, 599.07835, 431.33615, 607.98491], "spans": [[3, 1]], "text": "80.0", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [458.16544, 599.07835, 475.59998, 607.98491], "spans": [[3, 2]], "text": "66.0", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [497.02954000000005, 599.07835, 514.46411, 607.98491], "spans": [[3, 3]], "text": "73.0", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [346.55899, 611.03336, 395.47534, 619.93991], "spans": [[4, 0]], "text": "Acrobat Pro", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [413.90616, 611.03336, 431.34069999999997, 619.93991], "spans": [[4, 1]], "text": "68.9", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [458.16998000000007, 611.03336, 475.60452, 619.93991], "spans": [[4, 2]], "text": "61.8", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [497.03409, 611.03336, 514.46863, 619.93991], "spans": [[4, 3]], "text": "65.3", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [360.78101, 622.9883600000001, 381.25415, 631.89491], "spans": [[5, 0]], "text": "EDD", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [413.90158, 622.9883600000001, 431.33612, 631.89491], "spans": [[5, 1]], "text": "91.2", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [458.16541, 622.9883600000001, 475.59995000000004, 631.89491], "spans": [[5, 2]], "text": "85.4", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [497.0295100000001, 622.9883600000001, 514.46405, 631.89491], "spans": [[5, 3]], "text": "88.3", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [345.483, 634.94336, 396.5513, 643.84991], "spans": [[6, 0]], "text": "<PERSON><PERSON><PERSON><PERSON>", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [413.90616, 634.94336, 431.34069999999997, 643.84991], "spans": [[6, 1]], "text": "95.4", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [458.16998000000007, 634.94336, 475.60452, 643.84991], "spans": [[6, 2]], "text": "90.1", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [497.03400000000005, 634.82381, 514.46857, 643.78018], "spans": [[6, 3]], "text": "93.6", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [53.62853240966797, 499.60003662109375, 298.5574951171875, 573.0514221191406], "page": 8, "span": [0, 0], "__ref_s3_data": null}], "text": "Text is aligned to match original for ease of viewing", "type": "table", "payload": null, "#-cols": 6, "#-rows": 10, "data": [[{"bbox": null, "spans": [[0, 0]], "text": "", "type": "body"}, {"bbox": null, "spans": [[0, 1]], "text": "", "type": "body"}, {"bbox": [209.93285, 222.18073000000004, 241.04458999999997, 226.36212], "spans": [[0, 2], [0, 3]], "text": "論文ファイル", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [209.93285, 222.18073000000004, 241.04458999999997, 226.36212], "spans": [[0, 2], [0, 3]], "text": "論文ファイル", "type": "col_header", "col": 3, "col-header": true, "col-span": [2, 4], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [263.76489, 222.18073000000004, 284.50589, 226.36212], "spans": [[0, 4], [0, 5]], "text": "参考文献", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [263.76489, 222.18073000000004, 284.50589, 226.36212], "spans": [[0, 4], [0, 5]], "text": "参考文献", "type": "col_header", "col": 5, "col-header": true, "col-span": [4, 6], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [110.24990999999999, 229.66594999999995, 120.62018, 233.84735], "spans": [[1, 0]], "text": "出典", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [175.36609, 229.66594999999995, 196.1071, 233.84735], "spans": [[1, 1]], "text": "ファイル 数", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [209.62408, 229.66594999999995, 219.99435, 233.84735], "spans": [[1, 2]], "text": "英語", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [229.19814, 229.66594999999995, 244.75377, 233.84735], "spans": [[1, 3]], "text": "日本語", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [256.1142, 229.66594999999995, 266.48447, 233.84735], "spans": [[1, 4]], "text": "英語", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [278.38434, 229.66594999999995, 293.93997, 233.84735], "spans": [[1, 5]], "text": "日本語", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [55.53052099999999, 236.42584, 162.7131, 240.78375000000005], "spans": [[2, 0]], "text": "Association for Computational Linguistics(ACL2003)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [184.39731, 236.42584, 189.56456, 240.78375000000005], "spans": [[2, 1]], "text": "65", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [208.99026, 236.42584, 214.15752, 240.78375000000005], "spans": [[2, 2]], "text": "65", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [234.87517, 236.42584, 237.45833000000002, 240.78375000000005], "spans": [[2, 3]], "text": "0", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [256.88446, 236.42584, 264.6358, 240.78375000000005], "spans": [[2, 4]], "text": "150", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [284.06134, 236.42584, 286.6445, 240.78375000000005], "spans": [[2, 5]], "text": "0", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [55.53052099999999, 242.62048000000004, 139.72253, 246.97839], "spans": [[3, 0]], "text": "Computational Linguistics(COLING2002)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [183.10536, 242.62048000000004, 190.8567, 246.97839], "spans": [[3, 1]], "text": "140", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [207.69832, 242.62048000000004, 215.44965999999997, 246.97839], "spans": [[3, 2]], "text": "140", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [234.87517, 242.62048000000004, 237.45833000000002, 246.97839], "spans": [[3, 3]], "text": "0", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [256.88446, 242.62048000000004, 264.6358, 246.97839], "spans": [[3, 4]], "text": "150", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [284.06134, 242.62048000000004, 286.6445, 246.97839], "spans": [[3, 5]], "text": "0", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [55.53052099999999, 249.79845999999998, 97.013, 253.97986000000003], "spans": [[4, 0]], "text": "電気情報通信学会 2003 年総合大会", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [183.10536, 248.81506000000002, 190.8567, 253.17296999999996], "spans": [[4, 1]], "text": "150", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [210.28223, 248.81506000000002, 212.86539, 253.17296999999996], "spans": [[4, 2]], "text": "8", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [232.29153, 248.81506000000002, 240.04287999999997, 253.17296999999996], "spans": [[4, 3]], "text": "142", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [256.88446, 248.81506000000002, 264.6358, 253.17296999999996], "spans": [[4, 4]], "text": "223", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [281.47742, 248.81506000000002, 289.22876, 253.17296999999996], "spans": [[4, 5]], "text": "147", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [55.53052099999999, 257.28369, 91.827637, 261.46509000000003], "spans": [[5, 0]], "text": "情報処理学会第 65 回全国大会 (2003)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [183.10536, 256.30029, 190.8567, 260.65819999999997], "spans": [[5, 1]], "text": "177", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [210.28223, 256.30029, 212.86539, 260.65819999999997], "spans": [[5, 2]], "text": "1", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [232.29153, 256.30029, 240.04287999999997, 260.65819999999997], "spans": [[5, 3]], "text": "176", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [256.88446, 256.30029, 264.6358, 260.65819999999997], "spans": [[5, 4]], "text": "150", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [281.47742, 256.30029, 289.22876, 260.65819999999997], "spans": [[5, 5]], "text": "236", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [55.53052099999999, 264.5108, 60.715424, 268.69219999999996], "spans": [[6, 0]], "text": "第 17 回人工知能学会全国大会 (2003)", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [183.10536, 263.52739999999994, 190.8567, 267.88531], "spans": [[6, 1]], "text": "208", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [210.28223, 263.52739999999994, 212.86539, 267.88531], "spans": [[6, 2]], "text": "5", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [232.29153, 263.52739999999994, 240.04287999999997, 267.88531], "spans": [[6, 3]], "text": "203", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [256.88446, 263.52739999999994, 264.6358, 267.88531], "spans": [[6, 4]], "text": "152", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [281.47742, 263.52739999999994, 289.22876, 267.88531], "spans": [[6, 5]], "text": "244", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 6, "row-header": false, "row-span": [6, 7]}], [{"bbox": [55.53052099999999, 271.73785, 107.38374, 275.91925000000003], "spans": [[7, 0]], "text": "自然言語処理研究会第 146 〜 155 回", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 7, "row-header": true, "row-span": [7, 8]}, {"bbox": [184.39731, 270.75446, 189.56456, 275.11237000000006], "spans": [[7, 1]], "text": "98", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [210.28223, 270.75446, 212.86539, 275.11237000000006], "spans": [[7, 2]], "text": "2", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [233.58348, 270.75446, 238.75072999999998, 275.11237000000006], "spans": [[7, 3]], "text": "96", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [256.88446, 270.75446, 264.6358, 275.11237000000006], "spans": [[7, 4]], "text": "150", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 7, "row-header": false, "row-span": [7, 8]}, {"bbox": [281.47742, 270.75446, 289.22876, 275.11237000000006], "spans": [[7, 5]], "text": "232", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 7, "row-header": false, "row-span": [7, 8]}], [{"bbox": [55.53052099999999, 279.01392, 68.68605, 283.37183], "spans": [[8, 0]], "text": "WWW から収集した論文", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 8, "row-header": true, "row-span": [8, 9]}, {"bbox": [183.10536, 277.98157000000003, 190.8567, 282.33948000000004], "spans": [[8, 1]], "text": "107", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [208.99026, 277.98157000000003, 214.15752, 282.33948000000004], "spans": [[8, 2]], "text": "73", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [233.58348, 277.98157000000003, 238.75072999999998, 282.33948000000004], "spans": [[8, 3]], "text": "34", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [256.88446, 277.98157000000003, 264.6358, 282.33948000000004], "spans": [[8, 4]], "text": "147", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 8, "row-header": false, "row-span": [8, 9]}, {"bbox": [282.76938, 277.98157000000003, 287.93661, 282.33948000000004], "spans": [[8, 5]], "text": "96", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 8, "row-header": false, "row-span": [8, 9]}], [{"bbox": [169.61508, 286.45004, 174.79999, 290.63141], "spans": [[9, 0]], "text": "計", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 9, "row-header": true, "row-span": [9, 10]}, {"bbox": [183.10536, 285.46667, 190.8567, 289.8245800000001], "spans": [[9, 1]], "text": "945", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [207.69832, 285.46667, 215.44965999999997, 289.8245800000001], "spans": [[9, 2]], "text": "294", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [232.29153, 285.46667, 240.04287999999997, 289.8245800000001], "spans": [[9, 3]], "text": "651", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [255.76506, 285.46667, 265.75204, 289.8245800000001], "spans": [[9, 4]], "text": "1122", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 9, "row-header": false, "row-span": [9, 10]}, {"bbox": [281.47742, 285.46667, 289.22876, 289.8245800000001], "spans": [[9, 5]], "text": "955", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 9, "row-header": false, "row-span": [9, 10]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [304.9219970703125, 504.09930419921875, 550.2321166992188, 573.4851379394531], "page": 8, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 5, "#-rows": 7, "data": [[{"bbox": null, "spans": [[0, 0]], "text": "", "type": "body"}, {"bbox": [392.09671, 221.57446000000004, 438.0145, 226.63964999999996], "spans": [[0, 1], [0, 2]], "text": "Shares (in millions)", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [392.09671, 221.57446000000004, 438.0145, 226.63964999999996], "spans": [[0, 1], [0, 2]], "text": "Shares (in millions)", "type": "col_header", "col": 2, "col-header": true, "col-span": [1, 3], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [459.04861, 221.62415, 542.00018, 226.68933000000004], "spans": [[0, 3], [0, 4]], "text": "Weighted Average Grant Date Fair Value", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [459.04861, 221.62415, 542.00018, 226.68933000000004], "spans": [[0, 3], [0, 4]], "text": "Weighted Average Grant Date Fair Value", "type": "col_header", "col": 4, "col-header": true, "col-span": [3, 5], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": null, "spans": [[1, 0]], "text": "", "type": "body"}, {"bbox": [393.2442, 236.74712999999997, 400.74588, 241.81232], "spans": [[1, 1]], "text": "RS U s", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [427.18323, 236.74712999999997, 440.98778999999996, 241.81232], "spans": [[1, 2]], "text": "PSUs", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [468.38254, 236.74712999999997, 482.48465000000004, 241.81232], "spans": [[1, 3]], "text": "RSUs", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [516.92578, 236.74712999999997, 530.73035, 241.81232], "spans": [[1, 4]], "text": "PSUs", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [306.11493, 244.61084000000005, 355.6532, 249.67602999999997], "spans": [[2, 0]], "text": "Nonvested on Janua ry 1", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": true, "row-span": [2, 3]}, {"bbox": [396.24661, 244.91327, 400.75238, 249.97844999999995], "spans": [[2, 1]], "text": "1. 1", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [429.81838999999997, 244.91327, 437.32708999999994, 249.97844999999995], "spans": [[2, 2]], "text": "0.3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [465.52859, 244.91327, 478.40103, 249.97844999999995], "spans": [[2, 3]], "text": "90.10 $", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [513.44824, 244.91327, 531.46967, 249.97844999999995], "spans": [[2, 4]], "text": "$ 91.19", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [306.11493, 253.68451000000005, 325.62674, 258.74969], "spans": [[3, 0]], "text": "Granted", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": true, "row-span": [3, 4]}, {"bbox": [396.24661, 253.68451000000005, 400.75238, 258.74969], "spans": [[3, 1]], "text": "0. 5", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [429.81838999999997, 253.68451000000005, 437.32708999999994, 258.74969], "spans": [[3, 2]], "text": "0.1", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [466.43579000000005, 253.68451000000005, 482.54831, 258.74969], "spans": [[3, 3]], "text": "117.44", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [514.29065, 253.68451000000005, 530.80981, 258.74969], "spans": [[3, 4]], "text": "122.41", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [306.11493, 261.54822, 322.62866, 266.61339999999996], "spans": [[4, 0]], "text": "Vested", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": true, "row-span": [4, 5]}, {"bbox": [394.43222, 261.54822, 400.73563, 266.61339999999996], "spans": [[4, 1]], "text": "(0. 5 )", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [427.7016, 261.54822, 438.80563, 266.61339999999996], "spans": [[4, 2]], "text": "(0.1)", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [468.55533, 261.54822, 482.07043, 266.61339999999996], "spans": [[4, 3]], "text": "87.08", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [516.01862, 261.54822, 529.53375, 266.61339999999996], "spans": [[4, 4]], "text": "81.14", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [306.11493, 269.64148, 356.24771, 274.70667000000003], "spans": [[5, 0]], "text": "Canceled or forfeited", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": true, "row-span": [5, 6]}, {"bbox": [394.43222, 270.31946000000005, 400.73563, 275.38464], "spans": [[5, 1]], "text": "(0. 1 )", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [431.02802, 270.31946000000005, 436.4280099999999, 275.38464], "spans": [[5, 2]], "text": "-", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [465.83099000000004, 270.31946000000005, 482.35013, 275.38464], "spans": [[5, 3]], "text": "102.01", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [516.01862, 270.31946000000005, 529.53375, 275.38464], "spans": [[5, 4]], "text": "92.18", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}], [{"bbox": [306.11493, 278.48572, 373.35764, 283.55092999999994], "spans": [[6, 0]], "text": "Nonvested on December 31", "type": "row_header", "col": 0, "col-header": false, "col-span": [0, 1], "row": 6, "row-header": true, "row-span": [6, 7]}, {"bbox": [396.24661, 278.48572, 403.75531, 283.55092999999994], "spans": [[6, 1]], "text": "1.0", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [429.51599, 278.48572, 437.02469, 283.55092999999994], "spans": [[6, 2]], "text": "0.3", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [463.7142, 278.48572, 484.73965000000004, 283.55092999999994], "spans": [[6, 3]], "text": "104.85 $", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 6, "row-header": false, "row-span": [6, 7]}, {"bbox": [512.99463, 278.48572, 534.02008, 283.55092999999994], "spans": [[6, 4]], "text": "$ 104.51", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 6, "row-header": false, "row-span": [6, 7]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [84.0283203125, 577.6066589355469, 239.1690673828125, 635.6664581298828], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [82.92001342773438, 500.716064453125, 239.1903533935547, 558.2236785888672], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [83.94786071777344, 424.0904235839844, 239.17135620117188, 482.9522705078125], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [83.31756591796875, 304.7430114746094, 248.873046875, 395.9864501953125], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [310.3294372558594, 655.8524780273438, 555.8338623046875, 690.8223266601562], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "Figure 9: Example of a table with big empty distance between cells.", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [309.9566345214844, 607.2774658203125, 555.7466430664062, 637.3855133056641], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [309.9635314941406, 558.4485473632812, 555.7054443359375, 596.2945861816406], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [309.***********, 499.60601806640625, 425.9603271484375, 538.0946350097656], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [335.2694091796875, 354.97760009765625, 490.081787109375, 403.53253173828125], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "Figure 10: Example of a complex table with empty cells.", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [334.9334716796875, 289.2789001464844, 490.0914306640625, 338.0523681640625], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [335.2545471191406, 224.31207275390625, 490.22369384765625, 272.92431640625], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [333.9573669433594, 126.5096435546875, 518.4768676757812, 198.8865966796875], "page": 13, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [51.72642135620117, 447.7554931640625, 283.114013671875, 518.3907165527344], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [51.434879302978516, 300.17974853515625, 310.7267150878906, 338.51251220703125], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [50.86823654174805, 249.55401611328125, 310.6080017089844, 287.90374755859375], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [51.27280807495117, 200.086669921875, 311.0897216796875, 238.271484375], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [318.9809265136719, 577.3739471435547, 534.6229248046875, 630.7653961181641], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [319.0057678222656, 512.1423034667969, 534.408935546875, 565.8936614990234], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [328.1381530761719, 433.7275695800781, 523.8916015625, 503.3182067871094], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [319.4707946777344, 314.05645751953125, 518.5693359375, 361.09698486328125], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "Figure 14: Example with multi-line text.", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [319.982666015625, 256.30419921875, 519.0963745117188, 302.7562561035156], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [319.8287658691406, 198.8935546875, 519.6065673828125, 245.5906982421875], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [319.06494140625, 122.80792236328125, 533.77392578125, 182.1591796875], "page": 14, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [55.116363525390625, 542.6654510498047, 279.370849609375, 655.7449951171875], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [54.28135299682617, 418.4729309082031, 279.2568359375, 531.7384338378906], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [323.0059509277344, 569.0885925292969, 525.95166015625, 670.4528503417969], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "Figure 15: Example with triangular table.", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [323.384765625, 447.90789794921875, 526.1268920898438, 550.0270538330078], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [323.46868896484375, 327.739501953125, 525.9569091796875, 429.5491638183594], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [353.6920471191406, 156.22674560546875, 495.4288024902344, 304.594970703125], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}, {"prov": [{"bbox": [50.64818572998047, 160.736328125, 319.9103088378906, 286.01953125], "page": 15, "span": [0, 0], "__ref_s3_data": null}], "text": "", "type": "table", "payload": null, "#-cols": 0, "#-rows": 0, "data": [], "model": null, "bounding-box": null}], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}, {"height": 792.0, "page": 2, "width": 612.0}, {"height": 792.0, "page": 3, "width": 612.0}, {"height": 792.0, "page": 4, "width": 612.0}, {"height": 792.0, "page": 5, "width": 612.0}, {"height": 792.0, "page": 6, "width": 612.0}, {"height": 792.0, "page": 7, "width": 612.0}, {"height": 792.0, "page": 8, "width": 612.0}, {"height": 792.0, "page": 9, "width": 612.0}, {"height": 792.0, "page": 10, "width": 612.0}, {"height": 792.0, "page": 11, "width": 612.0}, {"height": 792.0, "page": 12, "width": 612.0}, {"height": 792.0, "page": 13, "width": 612.0}, {"height": 792.0, "page": 14, "width": 612.0}, {"height": 792.0, "page": 15, "width": 612.0}, {"height": 792.0, "page": 16, "width": 612.0}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}