{"_name": "", "type": "pdf-document", "description": {"title": null, "abstract": null, "authors": null, "affiliations": null, "subjects": null, "keywords": null, "publication_date": null, "languages": null, "license": null, "publishers": null, "url_refs": null, "references": null, "publication": null, "reference_count": null, "citation_count": null, "citation_date": null, "advanced": null, "analytics": null, "logs": [], "collection": null, "acquisition": null}, "file-info": {"filename": "2305.03393v1.pdf", "filename-prov": null, "document-hash": "c98927fda1ef9b66a4c3a236a65dc0cdf5c129be4122cdb58eaa3a37e3241eae", "#-pages": 14, "collection-name": null, "description": null, "page-hashes": [{"hash": "f09df98501fbcd8a2b359e4686187b56b7d82f3eb312cbbb23f61661691ecbf9", "model": "default", "page": 1}, {"hash": "6d26558563949e376cdb8dcb12a7288ec12d4c513de04616238aadcd15255d28", "model": "default", "page": 2}, {"hash": "4ef8043e938e362a06bc7f88f0b02df95d95cbfc891f544b7f88a448e53fb689", "model": "default", "page": 3}, {"hash": "8b755c3cd938ebf88bf14db6103c999794b0ca0c6f591f47a0c902b111159fe6", "model": "default", "page": 4}, {"hash": "95582f3138775a800969e873ad2e4eafca4f1d1de7b9b14ad826bbe8a17fe302", "model": "default", "page": 5}, {"hash": "619ab9fe3258434818f86df106cb76ed1fc8ab9800cbd91444098e91f7e67d8b", "model": "default", "page": 6}, {"hash": "c02e90eed528fcb71d0657183903b3e2035b86e3e750fb579f8c1f1e09aa132d", "model": "default", "page": 7}, {"hash": "b56262de55611de4494b0ed5011ce9567fada7c99bf53c5ff6c689ad9f941730", "model": "default", "page": 8}, {"hash": "680962e4a1193f15a591c82e1be59c0ff4cc78a066aeaaccad41f9262c67197b", "model": "default", "page": 9}, {"hash": "37dca86674661a5845a3bbd2fabb4a497cf2b5fc4908fd28dd63296c4fbee075", "model": "default", "page": 10}, {"hash": "0e3c057d1d7e6b359d73d4a44597879b2d421097da9aeb18ea581b32666ce740", "model": "default", "page": 11}, {"hash": "ec343c5522af29f238bde237ca655cdc018c5db20fb099c15ce8bc5045ce8593", "model": "default", "page": 12}, {"hash": "4ffa1d69b1366de506ca77c25a021790c3c150791fc830d6f4c85c3846efe6a9", "model": "default", "page": 13}, {"hash": "9fd62e0449eaf680e49767b4c512d8172cd3586480344318dc7e1cb0964b4d18", "model": "default", "page": 14}]}, "main-text": [{"prov": [{"bbox": [134.765, 645.4859, 480.59735, 676.10089], "page": 1, "span": [0, 60], "__ref_s3_data": null}], "text": "Optimized Table Tokenization for Table Structure Recognition", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [139.34305, 591.81409, 476.01270000000005, 622.30841], "page": 1, "span": [0, 222], "__ref_s3_data": null}], "text": "<PERSON><PERSON><PERSON> [0000 − 0002 − 3723 − $^{6960]}$, <PERSON>[0000 − 0002 − 9468 − $^{0822]}$, <PERSON><PERSON> [0000 − 0001 − 8513 − $^{3491]}$, <PERSON>[0000 − 0001 − 5761 − $^{0422]}$, [0000 − 0002 − 8088 − 0823]", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [229.52109000000002, 587.61926, 298.6087, 596.41626], "page": 1, "span": [0, 15], "__ref_s3_data": null}], "text": "and <PERSON>", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [279.1051, 566.72632, 336.25153, 574.79602], "page": 1, "span": [0, 12], "__ref_s3_data": null}], "text": "IBM Research", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [222.96609, 555.72247, 392.38983, 563.19147], "page": 1, "span": [0, 36], "__ref_s3_data": null}], "text": "{mly,ahn,nli,cau,taa}@zurich.ibm.com", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [163.1111, 327.26553, 452.24878000000007, 521.69885], "page": 1, "span": [0, 1198], "__ref_s3_data": null}], "text": "Abstract. Extracting tables from documents is a crucial task in any document conversion pipeline. Recently, transformer-based models have demonstrated that table-structure can be recognized with impressive accuracy using Image-to-Markup-Sequence (Im2Seq) approaches. Taking only the image of a table, such models predict a sequence of tokens (e.g. in HTML, LaTeX) which represent the structure of the table. Since the token representation of the table structure has a significant impact on the accuracy and run-time performance of any Im2Seq model, we investigate in this paper how table-structure representation can be optimised. We propose a new, optimised table-structure language (OTSL) with a minimized vocabulary and specific rules. The benefits of OTSL are that it reduces the number of tokens to 5 (HTML needs 28+) and shortens the sequence length to half of HTML on average. Consequently, model accuracy improves significantly, inference time is halved compared to HTML-based models, and the predicted table structures are always syntactically correct. This in turn eliminates most post-processing needs. Popular table structure data-sets will be published in OTSL format to the community.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [163.1111, 294.21451, 452.24158, 313.30606], "page": 1, "span": [0, 90], "__ref_s3_data": null}], "text": "Keywords: Table Structure Recognition · Data Representation · Transformers · Optimization.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76512, 259.31192, 228.93384, 269.88031], "page": 1, "span": [0, 14], "__ref_s3_data": null}], "text": "1 Introduction", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.76512, 163.18548999999996, 480.5959500000001, 243.71345999999994], "page": 1, "span": [0, 500], "__ref_s3_data": null}], "text": "Tables are ubiquitous in documents such as scientific papers, patents, reports, manuals, specification sheets or marketing material. They often encode highly valuable information and therefore need to be extracted with high accuracy. Unfortunately, tables appear in documents in various sizes, styling and structure, making it difficult to recover their correct structure with simple analytical methods. Therefore, accurate table extraction is achieved these days with machine-learning based methods.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76512, 127.14547000000005, 480.59583, 159.85244999999998], "page": 1, "span": [0, 235], "__ref_s3_data": null}], "text": "In modern document understanding systems [1,15], table extraction is typically a two-step process. Firstly, every table on a page is located with a bounding box, and secondly, their logical row and column structure is recognized. As of", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/0"}, {"prov": [{"bbox": [134.765, 591.77942, 480.59189, 665.66583], "page": 2, "span": [0, 574], "__ref_s3_data": null}], "text": "Fig. 1. Comparison between HTML and OTSL table structure representation: (A) table-example with complex row and column headers, including a 2D empty span, (B) minimal graphical representation of table structure using rectangular layout, (C) HTML representation, (D) OTSL representation. This example demonstrates many of the key-features of OTSL, namely its reduced vocabulary size (12 versus 5 in this case), its reduced sequence length (55 versus 30) and a enhanced internal structure (variable token sequence length per row in HTML versus a fixed length of rows in OTSL).", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 271.11330999999996, 480.59232000000003, 339.68622], "page": 2, "span": [0, 435], "__ref_s3_data": null}], "text": "today, table detection in documents is a well understood problem, and the latest state-of-the-art (SOTA) object detection methods provide an accuracy comparable to human observers [7,8,10,14,23]. On the other hand, the problem of table structure recognition (TSR) is a lot more challenging and remains a very active area of research, in which many novel machine learning algorithms are being explored [3,4,5,9,11,12,13,14,17,18,21,22].", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76501, 127.14530000000002, 480.59482, 267.44928000000004], "page": 2, "span": [0, 911], "__ref_s3_data": null}], "text": "Recently emerging SOTA methods for table structure recognition employ transformer-based models, in which an image of the table is provided to the network in order to predict the structure of the table as a sequence of tokens. These image-to-sequence (Im2Seq) models are extremely powerful, since they allow for a purely data-driven solution. The tokens of the sequence typically belong to a markup language such as HTML, Latex or Markdown, which allow to describe table structure as rows, columns and spanning cells in various configurations. In Figure 1, we illustrate how HTML is used to represent the table-structure of a particular example table. Public table-structure data sets such as PubTabNet [22], and FinTabNet [21], which were created in a semi-automated way from paired PDF and HTML sources (e.g. PubMed Central), popularized primarily the use of HTML as ground-truth representation format for TSR.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 580.58313, 480.59183, 673.06622], "page": 3, "span": [0, 584], "__ref_s3_data": null}], "text": "While the majority of research in TSR is currently focused on the development and application of novel neural model architectures, the table structure representation language (e.g. HTML in PubTabNet and FinTabNet) is usually adopted as is for the sequence tokenization in Im2Seq models. In this paper, we aim for the opposite and investigate the impact of the table structure representation language with an otherwise unmodified Im2Seq transformer-based architecture. Since the current state-of-the-art Im2Seq model is TableFormer [9], we select this model to perform our experiments.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 460.77014, 480.59572999999995, 577.16412], "page": 3, "span": [0, 721], "__ref_s3_data": null}], "text": "The main contribution of this paper is the introduction of a new optimised table structure language (OTSL), specifically designed to describe table-structure in an compact and structured way for Im2Seq models. OTSL has a number of key features, which make it very attractive to use in Im2Seq models. Specifically, compared to other languages such as HTML, OTSL has a minimized vocabulary which yields short sequence length, strong inherent structure (e.g. strict rectangular layout) and a strict syntax with rules that only look backwards. The latter allows for syntax validation during inference and ensures a syntactically correct table-structure. These OTSL features are illustrated in Figure 1, in comparison to HTML.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 352.91324, 480.59567, 457.35211], "page": 3, "span": [0, 626], "__ref_s3_data": null}], "text": "The paper is structured as follows. In section 2, we give an overview of the latest developments in table-structure reconstruction. In section 3 we review the current HTML table encoding (popularised by PubTabNet and FinTabNet) and discuss its flaws. Subsequently, we introduce OTSL in section 4, which includes the language definition, syntax rules and error-correction procedures. In section 5, we apply OTSL on the TableFormer architecture, compare it to TableFormer models trained on HTML and ultimately demonstrate the advantages of using OTSL. Finally, in section 6 we conclude our work and outline next potential steps.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 319.34366, 236.76912999999996, 329.91205], "page": 3, "span": [0, 14], "__ref_s3_data": null}], "text": "2 Related Work", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.76498, 127.14423, 484.12047999999993, 303.31418], "page": 3, "span": [0, 1161], "__ref_s3_data": null}], "text": "Approaches to formalize the logical structure and layout of tables in electronic documents date back more than two decades [16]. In the recent past, a wide variety of computer vision methods have been explored to tackle the problem of table structure recognition, i.e. the correct identification of columns, rows and spanning cells in a given table. Broadly speaking, the current deeplearning based approaches fall into three categories: object detection (OD) methods, Graph-Neural-Network (GNN) methods and Image-to-Markup-Sequence (Im2Seq) methods. Object-detection based methods [11,12,13,14,21] rely on tablestructure annotation using (overlapping) bounding boxes for training, and produce bounding-box predictions to define table cells, rows, and columns on a table image. Graph Neural Network (GNN) based methods [3,6,17,18], as the name suggests, represent tables as graph structures. The graph nodes represent the content of each table cell, an embedding vector from the table image, or geometric coordinates of the table cell. The edges of the graph define the relationship between the nodes, e.g. if they belong to the same column, row, or table cell.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 532.76208, 480.5957599999999, 673.06622], "page": 4, "span": [0, 939], "__ref_s3_data": null}], "text": "Other work [20] aims at predicting a grid for each table and deciding which cells must be merged using an attention network. Im2Seq methods cast the problem as a sequence generation task [4,5,9,22], and therefore need an internal tablestructure representation language, which is often implemented with standard markup languages (e.g. HTML, LaTeX, Markdown). In theory, Im2Seq methods have a natural advantage over the OD and GNN methods by virtue of directly predicting the table-structure. As such, no post-processing or rules are needed in order to obtain the table-structure, which is necessary with OD and GNN approaches. In practice, this is not entirely true, because a predicted sequence of table-structure markup does not necessarily have to be syntactically correct. Hence, depending on the quality of the predicted sequence, some post-processing needs to be performed to ensure a syntactically valid (let alone correct) sequence.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 305.3533, 480.59569999999997, 529.34308], "page": 4, "span": [0, 1404], "__ref_s3_data": null}], "text": "Within the Im2Seq method, we find several popular models, namely the encoder-dual-decoder model (EDD) [22], TableFormer [9], <PERSON><PERSON><PERSON><PERSON><PERSON>[2] and <PERSON> et. al. [19]. EDD uses two consecutive long short-term memory (LSTM) decoders to predict a table in HTML representation. The tag decoder predicts a sequence of HTML tags. For each decoded table cell ( <td> ), the attention is passed to the cell decoder to predict the content with an embedded OCR approach. The latter makes it susceptible to transcription errors in the cell content of the table. TableFormer address this reliance on OCR and uses two transformer decoders for HTML structure and cell bounding box prediction in an end-to-end architecture. The predicted cell bounding box is then used to extract text tokens from an originating (digital) PDF page, circumventing any need for OCR. TabSplitter [2] proposes a compact double-matrix representation of table rows and columns to do error detection and error correction of HTML structure sequences based on predictions from [19]. This compact double-matrix representation can not be used directly by the Img2seq model training, so the model uses HTML as an intermediate form. <PERSON> et. al. [4] introduce a data set and a baseline method using bidirectional LSTMs to predict LaTeX code. <PERSON><PERSON> [5] introduces Gated ResNet transformers to predict LaTeX code, and a separate OCR module to extract content.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 209.45133999999996, 480.59378, 301.93427], "page": 4, "span": [0, 572], "__ref_s3_data": null}], "text": "Im2Seq approaches have shown to be well-suited for the TSR task and allow a full end-to-end network design that can output the final table structure without pre- or post-processing logic. Furthermore, Im2Seq models have demonstrated to deliver state-of-the-art prediction accuracy [9]. This motivated the authors to investigate if the performance (both in accuracy and inference time) can be further improved by optimising the table structure representation language. We believe this is a necessary step before further improving neural network architectures for this task.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 175.88176999999996, 269.62442, 186.45015999999998], "page": 4, "span": [0, 19], "__ref_s3_data": null}], "text": "3 Problem Statement", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.76498, 127.14434000000006, 480.5936899999999, 159.85231], "page": 4, "span": [0, 233], "__ref_s3_data": null}], "text": "All known Im2Seq based models for TSR fundamentally work in similar ways. Given an image of a table, the Im2Seq model predicts the structure of the table by generating a sequence of tokens. These tokens originate from a finite vocab-", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 604.49316, 480.59378, 673.06622], "page": 5, "span": [0, 422], "__ref_s3_data": null}], "text": "ulary and can be interpreted as a table structure. For example, with the HTML tokens <table> , </table> , <tr> , </tr> , <td> and </td> , one can construct simple table structures without any spanning cells. In reality though, one needs at least 28 HTML tokens to describe the most common complex tables observed in real-world documents [21,22], due to a variety of spanning cells definitions in the HTML token vocabulary.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/1"}, {"prov": [{"bbox": [145.60701, 562.78821, 469.75223000000005, 570.92072], "page": 5, "span": [0, 73], "__ref_s3_data": null}], "text": "Fig. 2. Frequency of tokens in HTML and OTSL as they appear in PubTabNet.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 259.57941000000005, 480.59476, 423.79321], "page": 5, "span": [0, 1021], "__ref_s3_data": null}], "text": "Obviously, HTML and other general-purpose markup languages were not designed for Im2Seq models. As such, they have some serious drawbacks. First, the token vocabulary needs to be artificially large in order to describe all plausible tabular structures. Since most Im2Seq models use an autoregressive approach, they generate the sequence token by token. Therefore, to reduce inference time, a shorter sequence length is critical. Every table-cell is represented by at least two tokens ( <td> and </td> ). Furthermore, when tokenizing the HTML structure, one needs to explicitly enumerate possible column-spans and row-spans as words. In practice, this ends up requiring 28 different HTML tokens (when including column- and row-spans up to 10 cells) just to describe every table in the PubTabNet dataset. Clearly, not every token is equally represented, as is depicted in Figure 2. This skewed distribution of tokens in combination with variable token row-length makes it challenging for models to learn the HTML structure.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76501, 211.2944, 480.59289999999993, 255.95736999999997], "page": 5, "span": [0, 313], "__ref_s3_data": null}], "text": "Additionally, it would be desirable if the representation would easily allow an early detection of invalid sequences on-the-go, before the prediction of the entire table structure is completed. HTML is not well-suited for this purpose as the verification of incomplete sequences is non-trivial or even impossible.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76501, 127.14539000000002, 480.59473, 207.67336999999998], "page": 5, "span": [0, 542], "__ref_s3_data": null}], "text": "In a valid HTML table, the token sequence must describe a 2D grid of table cells, serialised in row-major ordering, where each row and each column have the same length (while considering row- and column-spans). Furthermore, every opening tag in HTML needs to be matched by a closing tag in a correct hierarchical manner. Since the number of tokens for each table row and column can vary significantly, especially for large tables with many row- and column-spans, it is complex to verify the consistency of predicted structures during sequence", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 652.31421, 480.59479, 673.06622], "page": 6, "span": [0, 132], "__ref_s3_data": null}], "text": "generation. Implicitly, this also means that Im2Seq models need to learn these complex syntax rules, simply to deliver valid output.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 496.25809, 480.59569999999997, 648.51721], "page": 6, "span": [0, 977], "__ref_s3_data": null}], "text": "In practice, we observe two major issues with prediction quality when training Im2Seq models on HTML table structure generation from images. On the one hand, we find that on large tables, the visual attention of the model often starts to drift and is not accurately moving forward cell by cell anymore. This manifests itself in either in an increasing location drift for proposed table-cells in later rows on the same column or even complete loss of vertical alignment, as illustrated in Figure 5. Addressing this with post-processing is partially possible, but clearly undesired. On the other hand, we find many instances of predictions with structural inconsistencies or plain invalid HTML output, as shown in Figure 6, which are nearly impossible to properly correct. Both problems seriously impact the TSR model performance, since they reflect not only in the task of pure structure recognition but also in the equally crucial recognition or matching of table cell content.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 460.80051, 372.50848, 471.3689], "page": 6, "span": [0, 36], "__ref_s3_data": null}], "text": "4 Optimised Table Structure Language", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.76498, 350.40015, 480.59473, 442.88303], "page": 6, "span": [0, 563], "__ref_s3_data": null}], "text": "To mitigate the issues with HTML in Im2Seq-based TSR models laid out before, we propose here our Optimised Table Structure Language (OTSL). OTSL is designed to express table structure with a minimized vocabulary and a simple set of rules, which are both significantly reduced compared to HTML. At the same time, OTSL enables easy error detection and correction during sequence generation. We further demonstrate how the compact structure representation and minimized sequence length improves prediction accuracy and inference time in the TableFormer architecture.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 317.32114, 261.80109, 326.12808], "page": 6, "span": [0, 23], "__ref_s3_data": null}], "text": "4.1 Language Definition", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.76498, 270.2941599999999, 480.58871, 303.00211], "page": 6, "span": [0, 165], "__ref_s3_data": null}], "text": "In Figure 3, we illustrate how the OTSL is defined. In essence, the OTSL defines only 5 tokens that directly describe a tabular structure based on an atomic 2D grid.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [149.70898, 257.70117000000005, 409.31137, 266.4981399999999], "page": 6, "span": [0, 57], "__ref_s3_data": null}], "text": "The OTSL vocabulary is comprised of the following tokens:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [140.99298, 235.22317999999996, 460.54443, 244.03011000000004], "page": 6, "span": [0, 72], "__ref_s3_data": null}], "text": "- -\"C\" cell a new table cell that either has or does not have cell content", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [140.99301, 210.67516999999998, 480.59392999999994, 231.4371], "page": 6, "span": [0, 82], "__ref_s3_data": null}], "text": "- -\"L\" cell left-looking cell , merging with the left neighbor cell to create a span", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [140.99304, 186.12617, 480.58856, 206.88810999999998], "page": 6, "span": [0, 81], "__ref_s3_data": null}], "text": "- -\"U\" cell up-looking cell , merging with the upper neighbor cell to create a span", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [140.99304, 173.53317000000004, 454.55496, 182.3401], "page": 6, "span": [0, 71], "__ref_s3_data": null}], "text": "- -\"X\" cell cross cell , to merge with both left and upper neighbor cells", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [140.99304, 160.93917999999996, 328.61676, 169.74610999999993], "page": 6, "span": [0, 40], "__ref_s3_data": null}], "text": "- -\"NL\" new-line , switch to the next row.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76505, 127.14516000000003, 480.59280000000007, 147.89714000000004], "page": 6, "span": [0, 99], "__ref_s3_data": null}], "text": "A notable attribute of OTSL is that it has the capability of achieving lossless conversion to HTML.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/2"}, {"prov": [{"bbox": [134.765, 636.15033, 480.5874, 666.2008100000002], "page": 7, "span": [0, 207], "__ref_s3_data": null}], "text": "Fig. 3. OTSL description of table structure: A - table example; B - graphical representation of table structure; C - mapping structure on a grid; D - OTSL structure encoding; E - explanation on cell encoding", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 477.89725, 246.65197999999998, 486.70419], "page": 7, "span": [0, 19], "__ref_s3_data": null}], "text": "4.2 Language Syntax", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 457.95526, 363.79617, 466.75223], "page": 7, "span": [0, 51], "__ref_s3_data": null}], "text": "The OTSL representation follows these syntax rules:", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [138.97299, 424.06625, 480.58902, 444.82919], "page": 7, "span": [0, 108], "__ref_s3_data": null}], "text": "- 1. Left-looking cell rule : The left neighbour of an \"L\" cell must be either another \"L\" cell or a \"C\" cell.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [138.97299, 400.15326000000005, 480.59229000000005, 420.91519], "page": 7, "span": [0, 106], "__ref_s3_data": null}], "text": "- 2. Up-looking cell rule : The upper neighbour of a \"U\" cell must be either another \"U\" cell or a \"C\" cell.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [138.97299, 388.19525, 226.07360999999997, 397.00219999999996], "page": 7, "span": [0, 20], "__ref_s3_data": null}], "text": "3. Cross cell rule :", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [151.70099, 352.32629, 480.59238, 385.03323], "page": 7, "span": [0, 167], "__ref_s3_data": null}], "text": "- The left neighbour of an \"X\" cell must be either another \"X\" cell or a \"U\" cell, and the upper neighbour of an \"X\" cell must be either another \"X\" cell or an \"L\" cell.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [138.97299, 340.36731, 474.59018, 349.17426], "page": 7, "span": [0, 78], "__ref_s3_data": null}], "text": "- 4. First row rule : Only \"L\" cells and \"C\" cells are allowed in the first row.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [138.97299, 316.45432, 480.58746, 337.21625], "page": 7, "span": [0, 84], "__ref_s3_data": null}], "text": "- 5. First column rule : Only \"U\" cells and \"C\" cells are allowed in the first column.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [138.97299, 292.54031, 480.59457, 313.30325], "page": 7, "span": [0, 144], "__ref_s3_data": null}], "text": "- 6. Rectangular rule : The table representation is always rectangular - all rows must have an equal number of tokens, terminated with \"NL\" token.", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76498, 151.05833000000007, 480.59583, 279.40729], "page": 7, "span": [0, 848], "__ref_s3_data": null}], "text": "The application of these rules gives OTSL a set of unique properties. First of all, the OTSL enforces a strictly rectangular structure representation, where every new-line token starts a new row. As a consequence, all rows and all columns have exactly the same number of tokens, irrespective of cell spans. Secondly, the OTSL representation is unambiguous: Every table structure is represented in one way. In this representation every table cell corresponds to a \"C\"-cell token, which in case of spans is always located in the top-left corner of the table cell definition. Third, OTSL syntax rules are only backward-looking. As a consequence, every predicted token can be validated straight during sequence generation by looking at the previously predicted sequence. As such, OTSL can guarantee that every predicted sequence is syntactically valid.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.76498, 127.14533000000006, 480.59265, 147.89731000000006], "page": 7, "span": [0, 153], "__ref_s3_data": null}], "text": "These characteristics can be easily learned by sequence generator networks, as we demonstrate further below. We find strong indications that this pattern", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 652.31421, 480.58884000000006, 673.06622], "page": 8, "span": [0, 84], "__ref_s3_data": null}], "text": "reduces significantly the column drift seen in the HTML based models (see Figure 5).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 621.63623, 319.34708, 630.44318], "page": 8, "span": [0, 35], "__ref_s3_data": null}], "text": "4.3 Error-detection and -mitigation", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 493.32416, 480.5957599999999, 609.7182], "page": 8, "span": [0, 797], "__ref_s3_data": null}], "text": "The design of OTSL allows to validate a table structure easily on an unfinished sequence. The detection of an invalid sequence token is a clear indication of a prediction mistake, however a valid sequence by itself does not guarantee prediction correctness. Different heuristics can be used to correct token errors in an invalid sequence and thus increase the chances for accurate predictions. Such heuristics can be applied either after the prediction of each token, or at the end on the entire predicted sequence. For example a simple heuristic which can correct the predicted OTSL sequence on-the-fly is to verify if the token with the highest prediction confidence invalidates the predicted sequence, and replace it by the token with the next highest confidence until OTSL rules are satisfied.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 460.26760999999993, 229.03534, 470.836], "page": 8, "span": [0, 13], "__ref_s3_data": null}], "text": "5 Experiments", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 340.3122900000001, 480.59528, 444.75015], "page": 8, "span": [0, 684], "__ref_s3_data": null}], "text": "To evaluate the impact of OTSL on prediction accuracy and inference times, we conducted a series of experiments based on the TableFormer model (Figure 4) with two objectives: Firstly we evaluate the prediction quality and performance of OTSL vs. HTML after performing Hyper Parameter Optimization (HPO) on the canonical PubTabNet data set. Secondly we pick the best hyper-parameters found in the first step and evaluate how OTSL impacts the performance of TableFormer after training on other publicly available data sets (FinTabNet, PubTables-1M [14]). The ground truth (GT) from all data sets has been converted into OTSL format for this purpose, and will be made publicly available.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/3"}, {"prov": [{"bbox": [134.76501, 288.26035, 480.59082, 307.35187], "page": 8, "span": [0, 104], "__ref_s3_data": null}], "text": "Fig. 4. Architecture sketch of the TableFormer model, which is a representative for the Im2Seq approach.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 127.14524000000006, 480.5917400000001, 171.80722000000003], "page": 8, "span": [0, 299], "__ref_s3_data": null}], "text": "We rely on standard metrics such as Tree Edit Distance score (TEDs) for table structure prediction, and Mean Average Precision (mAP) with 0.75 Intersection Over Union (IOU) threshold for the bounding-box predictions of table cells. The predicted OTSL structures were converted back to HTML format in", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 640.35822, 480.59579, 673.06622], "page": 9, "span": [0, 163], "__ref_s3_data": null}], "text": "order to compute the TED score. Inference timing results for all experiments were obtained from the same machine on a single core with AMD EPYC 7763 CPU @2.45 GHz.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 614.0072, 318.44843, 622.81415], "page": 9, "span": [0, 32], "__ref_s3_data": null}], "text": "5.1 Hyper Parameter Optimization", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.76498, 537.84113, 480.59277, 606.41418], "page": 9, "span": [0, 423], "__ref_s3_data": null}], "text": "We have chosen the PubTabNet data set to perform HPO, since it includes a highly diverse set of tables. Also we report TED scores separately for simple and complex tables (tables with cell spans). Results are presented in Table. 1. It is evident that with OTSL, our model achieves the same TED score and slightly better mAP scores in comparison to HTML. However OTSL yields a 2x speed up in the inference runtime over HTML.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/0"}, {"prov": [{"bbox": [134.76498, 464.9592, 480.5954, 516.92767], "page": 9, "span": [0, 398], "__ref_s3_data": null}], "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 275.04125999999997, 264.40332, 283.84821], "page": 9, "span": [0, 24], "__ref_s3_data": null}], "text": "5.2 Quantitative Results", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 174.96525999999994, 480.5957599999999, 267.44921999999997], "page": 9, "span": [0, 555], "__ref_s3_data": null}], "text": "We picked the model parameter configuration that produced the best prediction quality (enc=6, dec=6, heads=8) with PubTabNet alone, then independently trained and evaluated it on three publicly available data sets: PubTabNet (395k samples), FinTabNet (113k samples) and PubTables-1M (about 1M samples). Performance results are presented in Table. 2. It is clearly evident that the model trained on OTSL outperforms HTML across the board, keeping high TEDs and mAP scores even on difficult financial tables (FinTabNet) that contain sparse and large tables.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 127.14524000000006, 480.5957599999999, 171.80722000000003], "page": 9, "span": [0, 289], "__ref_s3_data": null}], "text": "Additionally, the results show that OTSL has an advantage over HTML when applied on a bigger data set like PubTables-1M and achieves significantly improved scores. Finally, OTSL achieves faster inference due to fewer decoding steps which is a result of the reduced sequence representation.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Table", "type": "table", "$ref": "#/tables/1"}, {"prov": [{"bbox": [134.765, 646.11334, 480.59357000000006, 676.16382], "page": 10, "span": [0, 192], "__ref_s3_data": null}], "text": "Table 2. TSR and cell detection results compared between OTSL and HTML on the PubTabNet [22], FinTabNet [21] and PubTables-1M [14] data sets using TableFormer [9] (with enc=6, dec=6, heads=8).", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 494.27826000000005, 257.08679, 503.08521], "page": 10, "span": [0, 23], "__ref_s3_data": null}], "text": "5.3 Qualitative Results", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 425.52231, 480.58981, 482.13922], "page": 10, "span": [0, 309], "__ref_s3_data": null}], "text": "To illustrate the qualitative differences between OTSL and HTML, Figure 5 demonstrates less overlap and more accurate bounding boxes with OTSL. In Figure 6, OTSL proves to be more effective in handling tables with longer token sequences, resulting in even more precise structure prediction and bounding boxes.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/4"}, {"prov": [{"bbox": [134.765, 352.28284, 480.59106, 394.40988], "page": 10, "span": [0, 270], "__ref_s3_data": null}], "text": "Fig. 5. The OTSL model produces more accurate bounding boxes with less overlap (E) than the HTML model (D), when predicting the structure of a sparse table (A), at twice the inference speed because of shorter sequence length (B),(C). \"PMC2807444_006_00.png\" PubTabNet. μ", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [227.91466, 116.65359999999998, 230.10028, 126.17397000000005], "page": 10, "span": [0, 1], "__ref_s3_data": null}], "text": "μ", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [300.58057, 98.57134199999996, 302.72638, 108.37805000000003], "page": 10, "span": [0, 1], "__ref_s3_data": null}], "text": "≥", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"name": "Picture", "type": "figure", "$ref": "#/figures/5"}, {"prov": [{"bbox": [134.765, 614.23236, 480.58838000000003, 666.2008100000002], "page": 11, "span": [0, 390], "__ref_s3_data": null}], "text": "Fig. 6. Visualization of predicted structure and detected bounding boxes on a complex table with many rows. The OTSL model (B) captured repeating pattern of horizontally merged cells from the GT (A), unlike the HTML model (C). The HTML model also didn't complete the HTML sequence correctly and displayed a lot more of drift and overlap of bounding boxes. \"PMC5406406_003_01.png\" PubTabNet.", "type": "caption", "payload": null, "name": "Caption", "font": null}, {"prov": [{"bbox": [134.765, 663.88263, 219.25478999999999, 674.4510500000001], "page": 12, "span": [0, 12], "__ref_s3_data": null}], "text": "6 Conclusion", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [134.765, 588.51819, 480.59569999999997, 645.13623], "page": 12, "span": [0, 330], "__ref_s3_data": null}], "text": "We demonstrated that representing tables in HTML for the task of table structure recognition with Im2Seq models is ill-suited and has serious limitations. Furthermore, we presented in this paper an Optimized Table Structure Language (OTSL) which, when compared to commonly used general purpose languages, has several key benefits.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 468.16321, 480.59479, 584.55621], "page": 12, "span": [0, 724], "__ref_s3_data": null}], "text": "First and foremost, given the same network configuration, inference time for a table-structure prediction is about 2 times faster compared to the conventional HTML approach. This is primarily owed to the shorter sequence length of the OTSL representation. Additional performance benefits can be obtained with HPO (hyper parameter optimization). As we demonstrate in our experiments, models trained on OTSL can be significantly smaller, e.g. by reducing the number of encoder and decoder layers, while preserving comparatively good prediction quality. This can further improve inference performance, yielding 5-6 times faster inference speed in OTSL with prediction quality comparable to models trained on HTML (see Table 1).", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 323.89734, 480.59482, 464.20117], "page": 12, "span": [0, 926], "__ref_s3_data": null}], "text": "Secondly, OTSL has more inherent structure and a significantly restricted vocabulary size. This allows autoregressive models to perform better in the TED metric, but especially with regards to prediction accuracy of the table-cell bounding boxes (see Table 2). As shown in Figure 5, we observe that the OTSL drastically reduces the drift for table cell bounding boxes at high row count and in sparse tables. This leads to more accurate predictions and a significant reduction in post-processing complexity, which is an undesired necessity in HTML-based Im2Seq models. Significant novelty lies in OTSL syntactical rules, which are few, simple and always backwards looking. Each new token can be validated only by analyzing the sequence of previous tokens, without requiring the entire sequence to detect mistakes. This in return allows to perform structural error detection and correction on-the-fly during sequence generation.", "type": "paragraph", "payload": null, "name": "Text", "font": null}, {"prov": [{"bbox": [134.765, 287.61078, 197.68642, 298.17917], "page": 12, "span": [0, 10], "__ref_s3_data": null}], "text": "References", "type": "subtitle-level-1", "payload": null, "name": "Section-header", "font": null}, {"prov": [{"bbox": [139.371, 228.12856, 480.5920100000001, 269.12014999999997], "page": 12, "span": [0, 270], "__ref_s3_data": null}], "text": "- 1. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C.B., Staar, P.W.J.: Delivering document conversion as a cloud service with high throughput and responsiveness. CoRR abs/2206.00785 (2022). https://doi.org/10.48550/arXiv.2206.00785 , https://doi.org/10.48550/arXiv.2206.00785", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.37097, 183.53439000000003, 480.5920100000001, 224.48116000000005], "page": 12, "span": [0, 301], "__ref_s3_data": null}], "text": "- 2. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Complex table structure recognition in the wild using transformer and identity matrix-based augmentation. In: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> (eds.) Frontiers in Handwriting Recognition. pp. 545561. Springer International Publishing, Cham (2022)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.37097, 160.81239000000005, 480.58731000000006, 179.84116000000006], "page": 12, "span": [0, 140], "__ref_s3_data": null}], "text": "- 3. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, X.L.: Complicated table structure recognition. arXiv preprint arXiv:1908.04729 (2019)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.37097, 127.13238999999999, 480.58826, 157.11915999999997], "page": 12, "span": [0, 204], "__ref_s3_data": null}], "text": "- 4. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Challenges in end-to-end neural scientific table recognition. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 894-901. IEEE (2019)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.371, 642.33832, 480.59479, 672.32599], "page": 13, "span": [0, 203], "__ref_s3_data": null}], "text": "- 5. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>: Tables to latex: structure and content extraction from scientific tables. International Journal on Document Analysis and Recognition (IJDAR) pp. 1-10 (2022)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.371, 598.49139, 480.59286000000003, 639.43805], "page": 13, "span": [0, 264], "__ref_s3_data": null}], "text": "- 6. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, N.I.: Table structure recognition based on grid shape graph. In: 2022 Asia-Pacific Signal and Information Processing Association Annual Summit and Conference (APSIPA ASC). pp. ********. IEEE (2022)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.371, 576.56244, 480.59012, 595.59113], "page": 13, "span": [0, 131], "__ref_s3_data": null}], "text": "- 7. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Tablebank: A benchmark dataset for table detection and recognition (2019)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.371, 521.71161, 480.59473, 573.66113], "page": 13, "span": [0, 345], "__ref_s3_data": null}], "text": "- 8. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, K., Staar, P.: Robust pdf document conversion using recurrent neural networks. Proceedings of the AAAI Conference on Artificial Intelligence 35 (17), 15137-15145 (May 2021), https://ojs.aaai.org/index.php/ AAAI/article/view/17777", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [139.371, 488.86746, 480.**************, 518.85516], "page": 13, "span": [0, 234], "__ref_s3_data": null}], "text": "- 9. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, P.: Tableformer: Table structure understanding with transformers. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4614-4623 (June 2022)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 423.05768, 480.59372, 485.96722000000005], "page": 13, "span": [0, 413], "__ref_s3_data": null}], "text": "- 10. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, A.<PERSON>., Staar, P.W.J.: Doclaynet: A large human-annotated dataset for document-layout segmentation. In: <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON> (eds.) KDD '22: The 28th ACM SIGKDD Conference on Knowledge Discovery and Data Mining, Washington, DC, USA, August 14 - 18, 2022. pp. 3743-3751. ACM (2022). https://doi.org/10.1145/3534678.3539043 , https:// doi.org/10.1145/3534678.3539043", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 379.25552, 480.59296, 420.20227], "page": 13, "span": [0, 295], "__ref_s3_data": null}], "text": "- 11. <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, M., Sultanpure, K.: Cascadetabnet: An approach for end to end table detection and structure recognition from imagebased documents. In: Proceedings of the IEEE/CVF conference on computer vision and pattern recognition workshops. pp. 572-573 (2020)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 335.40854, 480.59469999999993, 376.35529], "page": 13, "span": [0, 281], "__ref_s3_data": null}], "text": "- 12. <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, S.: Deepdesrt: Deep learning for detection and structure recognition of tables in document images. In: 2017 14th IAPR international conference on document analysis and recognition (ICDAR). vol. 1, pp. 1162-1167. IEEE (2017)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 291.51672, 480.59372, 332.5083], "page": 13, "span": [0, 275], "__ref_s3_data": null}], "text": "- 13. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, S.: Deeptabstr: Deep learning based table structure recognition. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 1403-1409 (2019). https:// doi.org/10.1109/ICDAR.2019.00226", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 247.71455000000003, 480.59286000000003, 288.66132], "page": 13, "span": [0, 241], "__ref_s3_data": null}], "text": "- 14. <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: PubTables-1M: Towards comprehensive table extraction from unstructured documents. In: Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR). pp. 4634-4642 (June 2022)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 181.90472, 480.59583, 244.81431999999995], "page": 13, "span": [0, 405], "__ref_s3_data": null}], "text": "- 15. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, C.: Corpus conversion service: A machine learning platform to ingest documents at scale. In: Proceedings of the 24th ACM SIGKDD International Conference on Knowledge Discovery & Data Mining. pp. 774-782. KDD '18, Association for Computing Machinery, New York, NY, USA (2018). https://doi.org/10.1145/3219819.3219834 , https://doi.org/10. 1145/3219819.3219834", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 160.02054999999996, 480.59542999999996, 179.04931999999997], "page": 13, "span": [0, 96], "__ref_s3_data": null}], "text": "- 16. <PERSON>, <PERSON><PERSON>: Tabular Abstraction, Editing, and Formatting. Ph.D. thesis, CAN (1996), aAINN09397", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76401, 127.13255000000004, 480.59119, 157.12032], "page": 13, "span": [0, 195], "__ref_s3_data": null}], "text": "- 17. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, D.: Res2tim: Reconstruct syntactic structures from table images. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 749-755. IEEE (2019)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.765, 642.33832, 480.59113, 672.32599], "page": 14, "span": [0, 223], "__ref_s3_data": null}], "text": "- 18. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.: Tgrnet: A table graph reconstruction network for table structure recognition. In: Proceedings of the IEEE/CVF International Conference on Computer Vision. pp. 1295-1304 (2021)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.765, 598.45752, 480.59469999999993, 639.44904], "page": 14, "span": [0, 269], "__ref_s3_data": null}], "text": "- 19. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Pingan-vcgroup's solution for icdar 2021 competition on scientific literature parsing task b: Table recognition to html (2021). https://doi.org/10.48550/ARXIV.2105.01848 , https://arxiv.org/abs/2105.01848", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76497, 576.58539, 480.5935400000001, 595.6131], "page": 14, "span": [0, 147], "__ref_s3_data": null}], "text": "- 20. <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>: Split, embed and merge: An accurate table structure recognizer. Pattern Recognition 126 , 108565 (2022)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76495, 521.74561, 480.59305000000006, 573.69611], "page": 14, "span": [0, 329], "__ref_s3_data": null}], "text": "- 21. <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>.X.<PERSON>.: Global table extractor (gte): A framework for joint table identification and cell structure recognition using visual context. In: 2021 IEEE Winter Conference on Applications of Computer Vision (WACV). pp. 697-706 (2021). https://doi.org/10.1109/WACV48630.2021. 00074", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76495, 477.9545, 480.59558, 518.90118], "page": 14, "span": [0, 259], "__ref_s3_data": null}], "text": "- 22. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, A.: Image-based table recognition: Data, model, and evaluation. In: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> (eds.) Computer Vision - ECCV 2020. pp. 564-580. Springer International Publishing, Cham (2020)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}, {"prov": [{"bbox": [134.76495, 445.07852, 480.59454, 475.06528], "page": 14, "span": [0, 206], "__ref_s3_data": null}], "text": "- 23. <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, A.J.: Publaynet: largest dataset ever for document layout analysis. In: 2019 International Conference on Document Analysis and Recognition (ICDAR). pp. 1015-1022. IEEE (2019)", "type": "paragraph", "payload": null, "name": "List-item", "font": null}], "figures": [{"prov": [{"bbox": [148.45364379882812, 366.1537780761719, 464.3608093261719, 583.6257629394531], "page": 2, "span": [0, 574], "__ref_s3_data": null}], "text": "Fig. 1. Comparison between HTML and OTSL table structure representation: (A) table-example with complex row and column headers, including a 2D empty span, (B) minimal graphical representation of table structure using rectangular layout, (C) HTML representation, (D) OTSL representation. This example demonstrates many of the key-features of OTSL, namely its reduced vocabulary size (12 versus 5 in this case), its reduced sequence length (55 versus 30) and a enhanced internal structure (variable token sequence length per row in HTML versus a fixed length of rows in OTSL).", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [137.41448974609375, 451.7695007324219, 476.5608215332031, 558.4876861572266], "page": 5, "span": [0, 73], "__ref_s3_data": null}], "text": "Fig. 2. Frequency of tokens in HTML and OTSL as they appear in PubTabNet.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [164.65028381347656, 511.6590576171875, 449.5505676269531, 628.2029113769531], "page": 7, "span": [0, 207], "__ref_s3_data": null}], "text": "Fig. 3. OTSL description of table structure: A - table example; B - graphical representation of table structure; C - mapping structure on a grid; D - OTSL structure encoding; E - explanation on cell encoding", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [140.70968627929688, 198.32281494140625, 472.73382568359375, 283.9361572265625], "page": 8, "span": [0, 104], "__ref_s3_data": null}], "text": "Fig. 4. Architecture sketch of the TableFormer model, which is a representative for the Im2Seq approach.", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [162.67430114746094, 128.78643798828125, 451.70062255859375, 347.***********], "page": 10, "span": [0, 270], "__ref_s3_data": null}], "text": "Fig. 5. The OTSL model produces more accurate bounding boxes with less overlap (E) than the HTML model (D), when predicting the structure of a sparse table (A), at twice the inference speed because of shorter sequence length (B),(C). \"PMC2807444_006_00.png\" PubTabNet. μ", "type": "figure", "payload": null, "bounding-box": null}, {"prov": [{"bbox": [168.39285278320312, 157.99432373046875, 447.35137939453125, 610.0334930419922], "page": 11, "span": [0, 390], "__ref_s3_data": null}], "text": "Fig. 6. Visualization of predicted structure and detected bounding boxes on a complex table with many rows. The OTSL model (B) captured repeating pattern of horizontally merged cells from the GT (A), unlike the HTML model (C). The HTML model also didn't complete the HTML sequence correctly and displayed a lot more of drift and overlap of bounding boxes. \"PMC5406406_003_01.png\" PubTabNet.", "type": "figure", "payload": null, "bounding-box": null}], "tables": [{"prov": [{"bbox": [139.***********, 322.5278625488281, 475.00372314453125, 454.4252014160156], "page": 9, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 1. HPO performed in OTSL and HTML representation on the same transformer-based TableFormer [9] architecture, trained only on PubTabNet [22]. Effects of reducing the # of layers in encoder and decoder stages of the model show that smaller models trained on OTSL perform better, especially in recognizing complex table structures, and maintain a much higher mAP score than the HTML counterpart.", "type": "table", "payload": null, "#-cols": 8, "#-rows": 6, "data": [[{"bbox": [160.37, 341.73495, 168.04793, 349.8047199999999], "spans": [[0, 0], [1, 0]], "text": "# enc-layers", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [207.974, 341.73495, 215.65193, 349.8047199999999], "spans": [[0, 1], [1, 1]], "text": "# dec-layers", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [239.79799999999997, 347.21396, 278.31766, 355.28372], "spans": [[0, 2], [1, 2]], "text": "Language", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [324.67001, 341.73495, 348.26419, 349.8047199999999], "spans": [[0, 3], [0, 4], [0, 5]], "text": "TEDs", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [324.67001, 341.73495, 348.26419, 349.8047199999999], "spans": [[0, 3], [0, 4], [0, 5]], "text": "TEDs", "type": "col_header", "col": 4, "col-header": true, "col-span": [3, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [324.67001, 341.73495, 348.26419, 349.8047199999999], "spans": [[0, 3], [0, 4], [0, 5]], "text": "TEDs", "type": "col_header", "col": 5, "col-header": true, "col-span": [3, 6], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [396.271, 341.73495, 417.12683, 349.8047199999999], "spans": [[0, 6]], "text": "mAP", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [430.771, 341.73495, 467.1423, 349.8047199999999], "spans": [[0, 7]], "text": "Inference", "type": "col_header", "col": 7, "col-header": true, "col-span": [7, 8], "row": 0, "row-header": false, "row-span": [0, 1]}], [{"bbox": [160.37, 341.73495, 168.04793, 349.8047199999999], "spans": [[0, 0], [1, 0]], "text": "# enc-layers", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [207.974, 341.73495, 215.65193, 349.8047199999999], "spans": [[0, 1], [1, 1]], "text": "# dec-layers", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [239.79799999999997, 347.21396, 278.31766, 355.28372], "spans": [[0, 2], [1, 2]], "text": "Language", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [286.686, 354.68594, 312.33261, 362.75570999999997], "spans": [[1, 3]], "text": "simple", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [320.702, 354.68594, 353.71988, 362.75570999999997], "spans": [[1, 4]], "text": "complex", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [369.306, 354.68594, 379.03094, 362.75570999999997], "spans": [[1, 5]], "text": "all", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [394.927, 352.69394000000005, 418.47278, 360.7637], "spans": [[1, 6]], "text": "(0.75)", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [427.14801, 352.69394000000005, 470.76056, 360.7637], "spans": [[1, 7]], "text": "time (secs)", "type": "col_header", "col": 7, "col-header": true, "col-span": [7, 8], "row": 1, "row-header": false, "row-span": [1, 2]}], [{"bbox": [161.90601, 373.51596, 166.51294, 381.58572], "spans": [[2, 0]], "text": "6", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [209.509, 373.51596, 214.11594, 381.58572], "spans": [[2, 1]], "text": "6", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [246.71000999999998, 368.03595, 271.40527, 376.10571], "spans": [[2, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [289.017, 368.03595, 310.00375, 376.10571], "spans": [[2, 3]], "text": "0.965 0.969", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [326.71701, 368.03595, 347.70377, 376.10571], "spans": [[2, 4]], "text": "0.934 0.927", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [363.67599, 368.03595, 384.66275, 376.10571], "spans": [[2, 5]], "text": "0.955 0.955", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [397.26999, 367.97317999999996, 416.12723, 375.89948], "spans": [[2, 6]], "text": "0.88 0.857", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [439.52701, 367.97317999999996, 458.38425, 375.89948], "spans": [[2, 7]], "text": "2.73 5.39", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [161.90601, 399.81696, 166.51294, 407.88672], "spans": [[3, 0]], "text": "4", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [209.509, 399.81696, 214.11594, 407.88672], "spans": [[3, 1]], "text": "4", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [246.71000999999998, 394.33795, 271.40527, 402.40771], "spans": [[3, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [289.017, 394.33795, 310.00375, 402.40771], "spans": [[3, 3]], "text": "0.938 0.952", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [326.71701, 394.33795, 347.70377, 402.40771], "spans": [[3, 4]], "text": "0.904 0.909", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [363.67599, 394.33795, 384.66275, 402.40771], "spans": [[3, 5]], "text": "0.927 0.938", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [394.61801, 394.27518, 418.77887, 402.20148], "spans": [[3, 6]], "text": "0.853 0.843", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [439.52701, 394.27518, 458.38425, 402.20148], "spans": [[3, 7]], "text": "1.97 3.77", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [161.90601, 426.11795, 166.51294, 434.1877099999999], "spans": [[4, 0]], "text": "2", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [209.509, 426.11795, 214.11594, 434.1877099999999], "spans": [[4, 1]], "text": "4", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [246.71000999999998, 420.63895, 271.40527, 428.70871], "spans": [[4, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [289.017, 420.63895, 310.00375, 428.70871], "spans": [[4, 3]], "text": "0.923 0.945", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [326.71701, 420.63895, 347.70377, 428.70871], "spans": [[4, 4]], "text": "0.897 0.901", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [363.67599, 420.63895, 384.66275, 428.70871], "spans": [[4, 5]], "text": "0.915 0.931", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [394.61801, 420.57617, 418.77887, 428.50247], "spans": [[4, 6]], "text": "0.859 0.834", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [439.52701, 420.57617, 458.38425, 428.50247], "spans": [[4, 7]], "text": "1.91 3.81", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 4, "row-header": false, "row-span": [4, 5]}], [{"bbox": [161.90601, 452.41995, 166.51294, 460.48972], "spans": [[5, 0]], "text": "4", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [209.509, 452.41995, 214.11594, 460.48972], "spans": [[5, 1]], "text": "2", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [246.71000999999998, 446.9399399999999, 271.40527, 455.0097], "spans": [[5, 2]], "text": "OTSL HTML", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [289.017, 446.9399399999999, 310.00375, 455.0097], "spans": [[5, 3]], "text": "0.952 0.944", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [329.021, 446.9399399999999, 345.40082, 455.0097], "spans": [[5, 4]], "text": "0.92 0.903", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [362.08801, 446.87717, 386.24887, 454.80347], "spans": [[5, 5]], "text": "0.942 0.931", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [394.61801, 446.87717, 418.77887, 454.80347], "spans": [[5, 6]], "text": "0.857 0.824", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 5, "row-header": false, "row-span": [5, 6]}, {"bbox": [439.52701, 446.87717, 458.38425, 454.80347], "spans": [[5, 7]], "text": "1.22 2", "type": "body", "col": 7, "col-header": false, "col-span": [7, 8], "row": 5, "row-header": false, "row-span": [5, 6]}]], "model": null, "bounding-box": null}, {"prov": [{"bbox": [143.6376495361328, 528.7375183105469, 470.8485412597656, 635.6522979736328], "page": 10, "span": [0, 0], "__ref_s3_data": null}], "text": "Table 2. TSR and cell detection results compared between OTSL and HTML on the PubTabNet [22], FinTabNet [21] and PubTables-1M [14] data sets using TableFormer [9] (with enc=6, dec=6, heads=8).", "type": "table", "payload": null, "#-cols": 7, "#-rows": 5, "data": [[{"bbox": [160.782, 166.55895999999996, 194.99779, 174.62865999999997], "spans": [[0, 0], [1, 0]], "text": "Data set", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [215.52499000000003, 166.534, 254.04465, 174.6037], "spans": [[0, 1], [1, 1]], "text": "Language", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [300.397, 161.07898, 323.99118, 169.14868], "spans": [[0, 2], [0, 3], [0, 4]], "text": "TEDs", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [300.397, 161.07898, 323.99118, 169.14868], "spans": [[0, 2], [0, 3], [0, 4]], "text": "TEDs", "type": "col_header", "col": 3, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [300.397, 161.07898, 323.99118, 169.14868], "spans": [[0, 2], [0, 3], [0, 4]], "text": "TEDs", "type": "col_header", "col": 4, "col-header": true, "col-span": [2, 5], "row": 0, "row-header": false, "row-span": [0, 1]}, {"bbox": [370.345, 166.55895999999996, 414.74661, 174.62865999999997], "spans": [[0, 5], [1, 5]], "text": "mAP(0.75)", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 0, "row-header": false, "row-span": [0, 2]}, {"bbox": [426.737, 161.07898, 463.10830999999996, 169.14868], "spans": [[0, 6], [1, 6]], "text": "Inference time (secs)", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 0, "row-header": false, "row-span": [0, 2]}], [{"bbox": [160.782, 166.55895999999996, 194.99779, 174.62865999999997], "spans": [[0, 0], [1, 0]], "text": "Data set", "type": "col_header", "col": 0, "col-header": true, "col-span": [0, 1], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [215.52499000000003, 166.534, 254.04465, 174.6037], "spans": [[0, 1], [1, 1]], "text": "Language", "type": "col_header", "col": 1, "col-header": true, "col-span": [1, 2], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [262.41299, 174.03101000000004, 288.0596, 182.10071000000005], "spans": [[1, 2]], "text": "simple", "type": "col_header", "col": 2, "col-header": true, "col-span": [2, 3], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [296.42899, 174.03101000000004, 329.44687, 182.10071000000005], "spans": [[1, 3]], "text": "complex", "type": "col_header", "col": 3, "col-header": true, "col-span": [3, 4], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [345.03299, 174.03101000000004, 354.75793, 182.10071000000005], "spans": [[1, 4]], "text": "all", "type": "col_header", "col": 4, "col-header": true, "col-span": [4, 5], "row": 1, "row-header": false, "row-span": [1, 2]}, {"bbox": [370.345, 166.55895999999996, 414.74661, 174.62865999999997], "spans": [[0, 5], [1, 5]], "text": "mAP(0.75)", "type": "col_header", "col": 5, "col-header": true, "col-span": [5, 6], "row": 1, "row-header": false, "row-span": [0, 2]}, {"bbox": [426.737, 161.07898, 463.10830999999996, 169.14868], "spans": [[0, 6], [1, 6]], "text": "Inference time (secs)", "type": "col_header", "col": 6, "col-header": true, "col-span": [6, 7], "row": 1, "row-header": false, "row-span": [0, 2]}], [{"bbox": [154.53799, 192.85999000000004, 201.24129, 200.92969000000005], "spans": [[2, 0]], "text": "PubTabNet", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [222.43700000000004, 187.38098000000002, 247.13226000000003, 195.45068000000003], "spans": [[2, 1]], "text": "OTSL HTML", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [264.74399, 187.38098000000002, 285.73074, 195.45068000000003], "spans": [[2, 2]], "text": "0.965 0.969", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [302.444, 187.38098000000002, 323.43076, 195.45068000000003], "spans": [[2, 3]], "text": "0.934 0.927", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [339.40302, 187.38098000000002, 360.38977, 195.45068000000003], "spans": [[2, 4]], "text": "0.955 0.955", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [383.116, 187.31817999999998, 401.97324, 195.24451], "spans": [[2, 5]], "text": "0.88 0.857", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 2, "row-header": false, "row-span": [2, 3]}, {"bbox": [435.49300999999997, 187.31817999999998, 454.35025, 195.24451], "spans": [[2, 6]], "text": "2.73 5.39", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 2, "row-header": false, "row-span": [2, 3]}], [{"bbox": [155.94501, 219.16198999999995, 199.83374, 227.23168999999996], "spans": [[3, 0]], "text": "FinTabNet", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [222.43700000000004, 213.68201, 247.13226000000003, 221.75171], "spans": [[3, 1]], "text": "OTSL HTML", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [264.74399, 213.68201, 285.73074, 221.75171], "spans": [[3, 2]], "text": "0.955 0.917", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [302.444, 213.68201, 323.43076, 221.75171], "spans": [[3, 3]], "text": "0.961 0.922", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [337.815, 213.61919999999998, 361.97586, 221.54552999999999], "spans": [[3, 4]], "text": "0.959 0.92", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [380.46399, 213.61919999999998, 404.62485, 221.54552999999999], "spans": [[3, 5]], "text": "0.862 0.722", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 3, "row-header": false, "row-span": [3, 4]}, {"bbox": [435.49300999999997, 213.61919999999998, 454.35025, 221.54552999999999], "spans": [[3, 6]], "text": "1.85 3.26", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 3, "row-header": false, "row-span": [3, 4]}], [{"bbox": [148.62601, 245.46294999999998, 207.1524, 253.53265], "spans": [[4, 0]], "text": "PubTables-1M", "type": "body", "col": 0, "col-header": false, "col-span": [0, 1], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [222.43700000000004, 239.98297000000002, 247.13226000000003, 248.05267000000003], "spans": [[4, 1]], "text": "OTSL HTML", "type": "body", "col": 1, "col-header": false, "col-span": [1, 2], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [264.74399, 239.98297000000002, 285.73074, 248.05267000000003], "spans": [[4, 2]], "text": "0.987 0.983", "type": "body", "col": 2, "col-header": false, "col-span": [2, 3], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [302.444, 239.98297000000002, 323.43076, 248.05267000000003], "spans": [[4, 3]], "text": "0.964 0.944", "type": "body", "col": 3, "col-header": false, "col-span": [3, 4], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [337.815, 239.92016999999998, 361.97586, 247.8465], "spans": [[4, 4]], "text": "0.977 0.966", "type": "body", "col": 4, "col-header": false, "col-span": [4, 5], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [380.46399, 239.92016999999998, 404.62485, 247.8465], "spans": [[4, 5]], "text": "0.896 0.889", "type": "body", "col": 5, "col-header": false, "col-span": [5, 6], "row": 4, "row-header": false, "row-span": [4, 5]}, {"bbox": [435.49300999999997, 239.92016999999998, 454.35025, 247.8465], "spans": [[4, 6]], "text": "1.79 3.26", "type": "body", "col": 6, "col-header": false, "col-span": [6, 7], "row": 4, "row-header": false, "row-span": [4, 5]}]], "model": null, "bounding-box": null}], "bitmaps": null, "equations": [], "footnotes": [], "page-dimensions": [{"height": 792.0, "page": 1, "width": 612.0}, {"height": 792.0, "page": 2, "width": 612.0}, {"height": 792.0, "page": 3, "width": 612.0}, {"height": 792.0, "page": 4, "width": 612.0}, {"height": 792.0, "page": 5, "width": 612.0}, {"height": 792.0, "page": 6, "width": 612.0}, {"height": 792.0, "page": 7, "width": 612.0}, {"height": 792.0, "page": 8, "width": 612.0}, {"height": 792.0, "page": 9, "width": 612.0}, {"height": 792.0, "page": 10, "width": 612.0}, {"height": 792.0, "page": 11, "width": 612.0}, {"height": 792.0, "page": 12, "width": 612.0}, {"height": 792.0, "page": 13, "width": 612.0}, {"height": 792.0, "page": 14, "width": 612.0}], "page-footers": [], "page-headers": [], "_s3_data": null, "identifiers": null}