Docling is available as an official [<PERSON><PERSON><PERSON>n](https://python.langchain.com/) extension.

To get started, check out the [step-by-step guide in LangChain][guide].

- 📖 [LangChain Docling integration docs][docs]
- 💻 [LangChain Docling integration GitHub][github]
- 🧑🏽‍🍳 [LangChain Docling integration example][example]
- 📦 [LangChain Docling integration PyPI][pypi]

[docs]: https://python.langchain.com/docs/integrations/providers/docling/
[github]: https://github.com/docling-project/docling-langchain
[guide]: https://python.langchain.com/docs/integrations/document_loaders/docling/
[example]: ../examples/rag_langchain.ipynb
[pypi]: https://pypi.org/project/langchain-docling/
