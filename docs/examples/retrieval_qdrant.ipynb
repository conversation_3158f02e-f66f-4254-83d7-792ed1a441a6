{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["<a href=\"https://colab.research.google.com/github/docling-project/docling/blob/main/docs/examples/hybrid_rag_qdrant\n", ".ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Retrieval with Qdrant"]}, {"cell_type": "markdown", "metadata": {}, "source": ["| Step | Tech | Execution | \n", "| --- | --- | --- |\n", "| Embedding | FastEmbed | 💻 Local |\n", "| Vector store | Qdrant | 💻 Local |"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This example demonstrates using Docling with [Qdrant](https://qdrant.tech/) to perform a hybrid search across your documents using dense and sparse vectors.\n", "\n", "We'll chunk the documents using Docling before adding them to a Qdrant collection. By limiting the length of the chunks, we can preserve the meaning in each vector embedding."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Setup"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 👉 Qdrant client uses [FastEmbed](https://github.com/qdrant/fastembed) to generate vector embeddings. You can install the `fastembed-gpu` package if you've got the hardware to support it."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["%pip install --no-warn-conflicts -q qdrant-client docling fastembed"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's import all the classes we'll be working with."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from qdrant_client import QdrantClient\n", "\n", "from docling.chunking import HybridChunker\n", "from docling.datamodel.base_models import InputFormat\n", "from docling.document_converter import DocumentConverter"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- For <PERSON><PERSON>, we'll set the  allowed formats to HTML since we'll only be working with webpages in this tutorial.\n", "- If we set a sparse model, Qdrant client will fuse the dense and sparse results using RRF. [Reference](https://qdrant.tech/documentation/tutorials/hybrid-search-fastembed/)."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/work/github.com/docling-project/docling/.venv/lib/python3.12/site-packages/huggingface_hub/utils/tqdm.py:155: UserWarning: Cannot enable progress bars: environment variable `HF_HUB_DISABLE_PROGRESS_BARS=1` is set and has priority.\n", "  warnings.warn(\n"]}], "source": ["COLLECTION_NAME = \"docling\"\n", "\n", "doc_converter = DocumentConverter(allowed_formats=[InputFormat.HTML])\n", "client = QdrantClient(location=\":memory:\")\n", "# The :memory: mode is a Python imitation of Qdrant's APIs for prototyping and CI.\n", "# For production deployments, use the Docker image: docker run -p 6333:6333 qdrant/qdrant\n", "# client = QdrantClient(location=\"http://localhost:6333\")\n", "\n", "client.set_model(\"sentence-transformers/all-MiniLM-L6-v2\")\n", "client.set_sparse_model(\"Qdrant/bm25\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now download and chunk the document using Docling. For demonstration, we'll use an article about chunking strategies :)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["result = doc_converter.convert(\n", "    \"https://www.sagacify.com/news/a-guide-to-chunking-strategies-for-retrieval-augmented-generation-rag\"\n", ")\n", "documents, metadatas = [], []\n", "for chunk in HybridChunker().chunk(result.document):\n", "    documents.append(chunk.text)\n", "    metadatas.append(chunk.meta.export_json_dict())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's now upload the documents to <PERSON><PERSON><PERSON>.\n", "\n", "- The `add()` method batches the documents and uses FastEmbed to generate vector embeddings on our machine."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["_ = client.add(\n", "    collection_name=COLLECTION_NAME,\n", "    documents=documents,\n", "    metadata=metadatas,\n", "    batch_size=64,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Retrieval"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["points = client.query(\n", "    collection_name=COLLECTION_NAME,\n", "    query_text=\"Can I split documents?\",\n", "    limit=10,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 0 ===\n", "Have you ever wondered how we, humans, would chunk? Here's a breakdown of a possible way a human would process a new document:\n", "1. We start at the top of the document, treating the first part as a chunk.\n", "   2. We continue down the document, deciding if a new sentence or piece of information belongs with the first chunk or should start a new one.\n", "    3. We keep this up until we reach the end of the document.\n", "The ultimate dream? Having an agent do this for you. But slow down! This approach is still being tested and isn't quite ready for the big leagues due to the time it takes to process multiple LLM calls and the cost of those calls. There's no implementation available in public libraries just yet. However, <PERSON> has his version available here.\n", "\n", "=== 1 ===\n", "Document Specific Chunking is a strategy that respects the document's structure. Rather than using a set number of characters or a recursive process, it creates chunks that align with the logical sections of the document, like paragraphs or subsections. This approach maintains the original author's organization of content and helps keep the text coherent. It makes the retrieved information more relevant and useful, particularly for structured documents with clearly defined sections.\n", "Document Specific Chunking can handle a variety of document formats, such as:\n", "<PERSON><PERSON>\n", "HTML\n", "Python\n", "etc\n", "Here we’ll take <PERSON><PERSON> as our example and use a modified version of our first sample text:\n", "‍\n", "The result is the following:\n", "You can see here that with a chunk size of 105, the Markdown structure of the document is taken into account, and the chunks thus preserve the semantics of the text!\n", "\n", "=== 2 ===\n", "And there you have it! These chunking strategies are like a personal toolbox when it comes to implementing Retrieval Augmented Generation. They're a ton of ways to slice and dice text, each with its unique features and quirks. This variety gives you the freedom to pick the strategy that suits your project best, allowing you to tailor your approach to perfectly fit the unique needs of your work.\n", "To put these strategies into action, there's a whole array of tools and libraries at your disposal. For example, llama_index is a fantastic tool that lets you create document indices and retrieve chunked documents. Let's not forget <PERSON><PERSON><PERSON><PERSON>, another remarkable tool that makes implementing chunking strategies a breeze, particularly when dealing with multi-language data. Diving into these tools and understanding how they can work in harmony with the chunking strategies we've discussed is a crucial part of mastering Retrieval Augmented Generation.\n", "By the way, if you're eager to experiment with your own examples using the chunking visualisation tool featured in this blog, feel free to give it a try! You can access it right here. Enjoy, and happy chunking! 😉\n", "\n", "=== 3 ===\n", "Retrieval Augmented Generation (RAG) has been a hot topic in understanding, interpreting, and generating text with AI for the last few months. It's like a wonderful union of retrieval-based and generative models, creating a playground for researchers, data scientists, and natural language processing enthusiasts, like you and me.\n", "To truly control the results produced by our RAG, we need to understand chunking strategies and their role in the process of retrieving and generating text. Indeed, each chunking strategy enhances RAG's effectiveness in its unique way.\n", "The goal of chunking is, as its name says, to chunk the information into multiple smaller pieces in order to store it in a more efficient and meaningful way. This allows the retrieval to capture pieces of information that are more related to the question at hand, and the generation to be more precise, but also less costly, as only a part of a document will be included in the LLM prompt, instead of the whole document.\n", "Let's explore some chunking strategies together.\n", "The methods mentioned in the article you're about to read usually make use of two key parameters. First, we have [chunk_size]— which controls the size of your text chunks. Then there's [chunk_overlap], which takes care of how much text overlaps between one chunk and the next.\n", "\n", "=== 4 ===\n", "Semantic Chun<PERSON> considers the relationships within the text. It divides the text into meaningful, semantically complete chunks. This approach ensures the information's integrity during retrieval, leading to a more accurate and contextually appropriate outcome.\n", "Semantic chunking involves taking the embeddings of every sentence in the document, comparing the similarity of all sentences with each other, and then grouping sentences with the most similar embeddings together.\n", "By focusing on the text's meaning and context, Semantic Chunking significantly enhances the quality of retrieval. It's a top-notch choice when maintaining the semantic integrity of the text is vital.\n", "However, this method does require more effort and is notably slower than the previous ones.\n", "On our example text, since it is quite short and does not expose varied subjects, this method would only generate a single chunk.\n", "\n", "=== 5 ===\n", "Language models used in the rest of your possible RAG pipeline have a token limit, which should not be exceeded. When dividing your text into chunks, it's advisable to count the number of tokens. Plenty of tokenizers are available. To ensure accuracy, use the same tokenizer for counting tokens as the one used in the language model.\n", "Consequently, there are also splitters available for this purpose.\n", "For instance, by using the [SpacyTextSplitter] from LangChain, the following chunks are created:\n", "‍\n", "\n", "=== 6 ===\n", "First things first, we have Character Chunking. This strategy divides the text into chunks based on a fixed number of characters. Its simplicity makes it a great starting point, but it can sometimes disrupt the text's flow, breaking sentences or words in unexpected places. Despite its limitations, it's a great stepping stone towards more advanced methods.\n", "Now let’s see that in action with an example. Imagine a text that reads:\n", "If we decide to set our chunk size to 100 and no chunk overlap, we'd end up with the following chunks. As you can see, Character Chunking can lead to some intriguing, albeit sometimes nonsensical, results, cutting some of the sentences in their middle.\n", "By choosing a smaller chunk size,  we would obtain more chunks, and by setting a bigger chunk overlap, we could obtain something like this:\n", "‍\n", "Also, by default this method creates chunks character by character based on the empty character [’ ’]. But you can specify a different one in order to chunk on something else, even a complete word! For instance, by specifying [' '] as the separator, you can avoid cutting words in their middle.\n", "\n", "=== 7 ===\n", "Next, let's take a look at Recursive Character Chunking. Based on the basic concept of Character Chunking, this advanced version takes it up a notch by dividing the text into chunks until a certain condition is met, such as reaching a minimum chunk size. This method ensures that the chunking process aligns with the text's structure, preserving more meaning. Its adaptability makes Recursive Character Chunking great for texts with varied structures.\n", "Again, let’s use the same example in order to illustrate this method. With a chunk size of 100, and the default settings for the other parameters, we obtain the following chunks:\n", "\n"]}], "source": ["for i, point in enumerate(points):\n", "    print(f\"=== {i} ===\")\n", "    print(point.document)\n", "    print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}