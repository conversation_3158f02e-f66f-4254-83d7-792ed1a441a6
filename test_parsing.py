#!/usr/bin/env python3
"""
Test document parsing functionality with actual files
"""

import requests
import json
import time
from pathlib import Path

def test_document_parsing_via_api(file_path, output_format="markdown"):
    """Test document parsing via the API"""
    try:
        if not Path(file_path).exists():
            print(f"❌ File not found: {file_path}")
            return False
            
        print(f"🔄 Testing parsing of: {file_path}")
        print(f"📄 File size: {Path(file_path).stat().st_size} bytes")
        
        with open(file_path, 'rb') as f:
            files = {'file': (Path(file_path).name, f, 'application/octet-stream')}
            data = {
                'enable_ocr': 'false',
                'enable_table_structure': 'true',
                'output_format': output_format
            }
            
            start_time = time.time()
            response = requests.post(
                "http://localhost:8081/process-document/",
                files=files,
                data=data,
                timeout=60  # 60 second timeout
            )
            processing_time = time.time() - start_time
            
            print(f"⏱️  Processing time: {processing_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                print("✅ Document parsing successful!")
                print(f"   Success: {result.get('success', 'Unknown')}")
                print(f"   Filename: {result.get('filename', 'Unknown')}")
                print(f"   Output format: {result.get('output_format', 'Unknown')}")
                print(f"   Content length: {len(result.get('content', ''))}")
                
                # Document structure info
                if 'document_structure' in result:
                    struct = result['document_structure']
                    print(f"   Pages: {result.get('page_count', 0)}")
                    print(f"   Tables: {struct.get('tables_count', 0)}")
                    print(f"   Figures: {struct.get('figures_count', 0)}")
                    print(f"   Text items: {struct.get('text_items_count', 0)}")
                
                # Processing info
                if 'processing_info' in result:
                    proc_info = result['processing_info']
                    print(f"   Conversion status: {proc_info.get('conversion_status', 'Unknown')}")
                    print(f"   Backend used: {proc_info.get('backend_used', 'Unknown')}")
                
                # Show first part of content
                content = result.get('content', '')
                if content:
                    print("\n📝 First 500 characters of parsed content:")
                    print("-" * 60)
                    print(content[:500])
                    if len(content) > 500:
                        print("...")
                    print("-" * 60)
                    
                    # Save full content to file for inspection
                    output_file = f"parsed_output_{Path(file_path).stem}.{output_format}"
                    with open(output_file, 'w', encoding='utf-8') as out_f:
                        out_f.write(content)
                    print(f"💾 Full parsed content saved to: {output_file}")
                
                return True
            else:
                print(f"❌ API returned error status: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"   Error: {error_info.get('error', 'Unknown error')}")
                    print(f"   Message: {error_info.get('message', 'No message')}")
                except:
                    print(f"   Response text: {response.text}")
                return False
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out - document processing took too long")
        return False
    except Exception as e:
        print(f"❌ Parsing test failed: {e}")
        return False

def test_health_first():
    """Test health endpoint first"""
    try:
        response = requests.get("http://localhost:8081/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Server health check passed")
            print(f"   Status: {health_data.get('status', 'Unknown')}")
            print(f"   Converter initialized: {health_data.get('converter_initialized', False)}")
            return True
        else:
            print(f"❌ Health check failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def main():
    """Test document parsing with available files"""
    print("🧪 Testing Document Parsing Functionality")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. Testing server health...")
    if not test_health_first():
        print("❌ Server not healthy. Exiting.")
        return
    
    # Test 2: Find files to test
    print("\n2. Looking for files to test...")
    
    # List of potential test files
    test_files = [
        "sample_document.md",
        "README.md",
        "WEB_UI_README.md",
        "CONTRIBUTING.md",
        "LICENSE"
    ]
    
    # Find existing files
    available_files = []
    for file_path in test_files:
        if Path(file_path).exists():
            available_files.append(file_path)
            print(f"   Found: {file_path}")
    
    if not available_files:
        print("❌ No test files found")
        return
    
    # Test 3: Parse each available file
    print(f"\n3. Testing parsing of {len(available_files)} files...")
    
    success_count = 0
    for i, file_path in enumerate(available_files, 1):
        print(f"\n--- Test {i}/{len(available_files)} ---")
        if test_document_parsing_via_api(file_path):
            success_count += 1
        print()
    
    # Summary
    print("=" * 60)
    print(f"🏁 Parsing test completed: {success_count}/{len(available_files)} files parsed successfully")
    
    if success_count == len(available_files):
        print("✅ All document parsing tests passed!")
    else:
        print(f"⚠️  {len(available_files) - success_count} files failed to parse")

if __name__ == "__main__":
    main()
