#!/usr/bin/env python3
"""
Bidbees Web UI Startup Script

This script starts the Bidbees web interface on port 8081.
It provides a user-friendly web interface for processing large documents (300-400 pages).
"""

import uvicorn
import sys
import os
from pathlib import Path

def main():
    """Start the Bidbees Web UI server"""
    
    # Add the current directory to Python path to ensure imports work
    current_dir = Path(__file__).parent
    sys.path.insert(0, str(current_dir))
    
    # Ensure the API directories exist
    api_dir = current_dir / "api"
    static_dir = api_dir / "static"
    templates_dir = api_dir / "templates"
    
    static_dir.mkdir(parents=True, exist_ok=True)
    templates_dir.mkdir(parents=True, exist_ok=True)
    
    # Try different ports if 8081 is in use
    ports_to_try = [8081, 8082, 8083, 8084, 8085]

    for port in ports_to_try:
        print("🚀 Starting Bidbees Web UI...")
        print(f"📁 Working directory: {current_dir}")
        print(f"🌐 Trying port {port}...")
        print(f"🌐 Server will be available at: http://localhost:{port}")
        print(f"📊 API documentation at: http://localhost:{port}/docs")
        print("📄 Optimized for large documents (300-400 pages)")
        print("=" * 60)

        try:
            # Start the server
            uvicorn.run(
                "api.main:app",
                host="0.0.0.0",
                port=port,
                reload=True,  # Enable auto-reload for development
                log_level="info",
                access_log=True
            )
            break  # If successful, break out of the loop
        except OSError as e:
            if "Address already in use" in str(e):
                print(f"❌ Port {port} is already in use, trying next port...")
                continue
            else:
                raise e
        except KeyboardInterrupt:
            print("\n👋 Shutting down Bidbees Web UI...")
            break
        except Exception as e:
            print(f"❌ Error starting server: {e}")
            sys.exit(1)

if __name__ == "__main__":
    main()
