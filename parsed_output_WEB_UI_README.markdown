# Docling Web UI

A modern, interactive web interface for the Docling document processing system. This web UI provides an easy-to-use interface for uploading, processing, and analyzing documents using the powerful Docling SDK.

## 🌟 Features

### Document Processing

- [&lt;RawText children='Multi-format Support'&gt;]
- [&lt;RawText children='Advanced OCR'&gt;]
- [&lt;RawText children='Table Structure Detection'&gt;]
- [&lt;RawText children='Multiple Output Formats'&gt;]

### User Interface

- [&lt;RawText children='Drag &amp; Drop Upload'&gt;]
- [&lt;RawText children='Real-time Processing'&gt;]
- [&lt;RawText children='Interactive Results'&gt;]
- [&lt;RawText children='Document Statistics'&gt;]
- [&lt;RawText children='Responsive Design'&gt;]

### Technical Features

- [&lt;RawText children='RESTful API'&gt;]
- [&lt;RawText children='Health Monitoring'&gt;]
- [&lt;RawText children='Error Handling'&gt;]
- [&lt;RawText children='Performance Optimized'&gt;]

## 🚀 Quick Start

### Prerequisites

- Python 3.9 or higher
- Poetry (for dependency management)
- All Docling dependencies installed

### Installation

1. [&lt;RawText children='Install Dependencies'&gt;]
2. ```
# Install Jinja2 for templating (if not already installed)
poetry add jinja2

# Or using pip
pip install jinja2
```
3. [&lt;RawText children='Start the Web UI'&gt;]
4. ```
# Using the startup script (recommended)
python start_web_ui.py

# Or directly with uvicorn
uvicorn api.main:app --host 0.0.0.0 --port 8081 --reload
```
5. [&lt;RawText children='Access the Interface'&gt;]
    - Web UI: http://localhost:8081
    - API Documentation: http://localhost:8081/docs
    - Health Check: http://localhost:8081/health

## 📖 Usage Guide

### Basic Document Processing

1. [&lt;RawText children='Upload Document'&gt;]
    - Click "Select Document" or drag &amp; drop a file
    - Supported formats: PDF, DOCX, PPTX, XLSX, HTML, MD, CSV, PNG, JPG, JPEG
2. [&lt;RawText children='Configure Options'&gt;]
    - [&lt;RawText children='Enable OCR'&gt;]
    - [&lt;RawText children='Table Structure Detection'&gt;]
    - [&lt;RawText children='Output Format'&gt;]
3. [&lt;RawText children='Process Document'&gt;]
    - Click "Process Document" to start conversion
    - Monitor progress with the real-time progress bar
    - View results in the right panel
4. [&lt;RawText children='Review Results'&gt;]
    - View extracted content in your chosen format
    - Check document statistics (pages, tables, figures, text items)
    - Copy content to clipboard or download as file

### Advanced Features

#### API Access

The web UI exposes a full RESTful API:

```
# Process a document via API
curl -X POST "http://localhost:8081/process-document/" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.pdf" \
     -F "enable_ocr=false" \
     -F "enable_table_structure=true" \
     -F "output_format=markdown"
```

#### Health Monitoring

```
# Check system health
curl http://localhost:8081/health
```

#### Supported Formats

```
# Get list of supported formats
curl http://localhost:8081/api/formats
```

## 🔧 Configuration

### Environment Variables

- DOCLING\_ARTIFACTS\_PATH
- PYTHONPATH

### Processing Options

- [&lt;RawText children='OCR Settings'&gt;]
- [&lt;RawText children='Table Detection'&gt;]
- [&lt;RawText children='Output Formats'&gt;]
- [&lt;RawText children='Accelerator'&gt;]

## 📁 File Structure

```
api/
├── main.py              # FastAPI application
├── static/
│   ├── style.css        # UI styling
│   └── script.js        # Frontend JavaScript
└── templates/
    └── index.html       # Main UI template

start_web_ui.py          # Startup script
WEB_UI_README.md         # This documentation
```

## 🛠️ Development

### Running in Development Mode

```
# Start with auto-reload
python start_web_ui.py

# Or with uvicorn directly
uvicorn api.main:app --host 0.0.0.0 --port 8081 --reload
```

### Customization

- [&lt;RawText children='Styling'&gt;]
- [&lt;RawText children='Functionality'&gt;]
- [&lt;RawText children='Layout'&gt;]
- [&lt;RawText children='API'&gt;]

## 🐳 Docker Deployment

The existing Dockerfile has been updated to support the web UI:

```
# Build the image
docker build -t docling-web-ui .

# Run the container
docker run -p 8081:8081 docling-web-ui

# Or use docker-compose (update port mapping)
docker-compose up
```

## 🔍 Troubleshooting

### Common Issues

1. [&lt;RawText children='Port Already in Use'&gt;]
2. ```
# Check what's using port 8081
lsof -i :8081

# Kill the process or use a different port
uvicorn api.main:app --host 0.0.0.0 --port 8082
```
3. [&lt;RawText children='Import Errors'&gt;]
4. ```
# Ensure you're in the project root directory
cd /path/to/docling
python start_web_ui.py
```
5. [&lt;RawText children='Missing Dependencies'&gt;]
6. ```
# Install missing packages
poetry install
# or
pip install fastapi uvicorn jinja2 python-multipart
```
7. [&lt;RawText children='File Upload Issues'&gt;]
    - Check file size limits
    - Verify file format is supported
    - Ensure sufficient disk space

### Performance Tips

- Enable table structure detection only when needed
- Use OCR sparingly for better performance
- Process smaller documents for faster results
- Monitor system resources during processing

## 📊 API Endpoints

| Endpoint   | Method   | Description   |
|------------|----------|---------------|
|            |          |               |

```
/
```

```
/process-document/
```

```
/health
```

```
/api/formats
```

```
/docs
```

## 🤝 Contributing

To contribute to the web UI:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This web UI is part of the Docling project and follows the same MIT license.

Happy Document Processing! 🚀