#!/usr/bin/env python3
"""
Test script to test the API endpoint directly
"""

import requests
import json
from pathlib import Path

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get("http://localhost:8081/health", timeout=10)
        print(f"Health endpoint status: {response.status_code}")
        print(f"Health response: {response.json()}")
        return response.status_code == 200
    except Exception as e:
        print(f"❌ Health endpoint failed: {e}")
        return False

def test_document_processing():
    """Test document processing via API"""
    try:
        # Create a simple test file
        test_content = """# Test Document

This is a simple test document to verify that the API is working properly.

## Section 1
Some content here.

## Section 2
More content here.
"""
        
        test_file = Path("test_api_document.md")
        test_file.write_text(test_content)
        
        # Test the API endpoint
        with open(test_file, 'rb') as f:
            files = {'file': ('test_document.md', f, 'text/markdown')}
            data = {
                'enable_ocr': 'false',
                'enable_table_structure': 'true',
                'output_format': 'markdown'
            }
            
            print("🔄 Testing document processing via API...")
            response = requests.post(
                "http://localhost:8081/process-document/",
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"API response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ API processing successful!")
                print(f"Success: {result.get('success', 'Unknown')}")
                print(f"Filename: {result.get('filename', 'Unknown')}")
                print(f"Output format: {result.get('output_format', 'Unknown')}")
                print(f"Content length: {len(result.get('content', ''))}")
                
                if result.get('content'):
                    print("First 200 characters of content:")
                    print(result['content'][:200])
                
                # Cleanup
                test_file.unlink()
                return True
            else:
                print(f"❌ API returned error status: {response.status_code}")
                print(f"Response: {response.text}")
                test_file.unlink()
                return False
                
    except Exception as e:
        print(f"❌ API test failed: {e}")
        if test_file.exists():
            test_file.unlink()
        return False

def main():
    """Run API tests"""
    print("🧪 Testing Docling Web API")
    print("=" * 50)
    
    # Test 1: Health endpoint
    print("\n1. Testing health endpoint...")
    if not test_health_endpoint():
        print("❌ Health endpoint test failed. Is the server running?")
        return
    
    # Test 2: Document processing
    print("\n2. Testing document processing...")
    if test_document_processing():
        print("✅ All API tests passed!")
    else:
        print("❌ Document processing test failed.")
    
    print("\n" + "=" * 50)
    print("🏁 API test completed.")

if __name__ == "__main__":
    main()
