version: '3.8'

services:
  docling-api:
    # Build the image using the Dockerfile in the current directory
    build: .
    image: docling-api:latest # Optional: tag the built image
    container_name: docling-api-service
    environment:
      # Use the persistent artifacts volume
      - DOCLING_ARTIFACTS_PATH=/artifacts
    volumes:
      # Persist models and artifacts between container restarts
      - docling_artifacts:/artifacts
    ports:
      # Map port 8081 on your local machine to port 8081 in the container
      - "8081:8081"
    # Optional: For better reliability in production
    restart: unless-stopped

volumes:
  docling_artifacts:
