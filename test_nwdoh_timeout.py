#!/usr/bin/env python3
"""
Test the NWDOH document with the timeout-enabled server
"""

import requests
import json
import time
from pathlib import Path

def test_nwdoh_document():
    """Test the NWDOH document with timeout handling"""
    
    # Test the new timeout-enabled server
    server_url = "http://localhost:8082"
    
    print("🧪 Testing NWDOH Document with Timeout Handling")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. Testing server health...")
    try:
        response = requests.get(f"{server_url}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Server is healthy")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Version: {health_data.get('version')}")
            print(f"   Converter available: {health_data.get('converter_initialized')}")
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False
    
    # Test 2: Manual file input
    print("\n2. Looking for NWDOH document...")
    
    # Ask user for file path since we can't find it automatically
    print("Please provide the path to the RE-ADVERT NWDOH 05 2025.pdf file:")
    print("(You can drag and drop the file here, or type the full path)")
    
    file_path = input("File path: ").strip().strip('"').strip("'")
    
    if not file_path:
        print("❌ No file path provided")
        return False
    
    file_path = Path(file_path)
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    file_size_mb = file_path.stat().st_size / (1024 * 1024)
    print(f"✅ Found file: {file_path.name}")
    print(f"📄 File size: {file_size_mb:.2f} MB")
    
    # Test 3: Process the document with different scenarios
    print(f"\n3. Testing document processing...")
    
    scenarios = [
        {"ocr": False, "table_structure": True, "format": "markdown", "name": "No OCR, Tables ON"},
        {"ocr": False, "table_structure": False, "format": "markdown", "name": "No OCR, Tables OFF"},
    ]
    
    results = []
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n--- Scenario {i}: {scenario['name']} ---")
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (file_path.name, f, 'application/pdf')}
                data = {
                    'enable_ocr': str(scenario['ocr']).lower(),
                    'enable_table_structure': str(scenario['table_structure']).lower(),
                    'output_format': scenario['format']
                }
                
                print("⏳ Sending request to timeout-enabled server...")
                start_time = time.time()
                
                response = requests.post(
                    f"{server_url}/process-document/",
                    files=files,
                    data=data,
                    timeout=180  # 3 minute client timeout
                )
                
                total_time = time.time() - start_time
                print(f"⏱️  Total request time: {total_time:.2f} seconds")
                print(f"📊 Response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    
                    print("✅ Processing successful!")
                    print(f"   Filename: {result.get('filename')}")
                    print(f"   Pages: {result.get('page_count', 0)}")
                    print(f"   Content length: {len(result.get('content', ''))}")
                    
                    # Document structure
                    if 'document_structure' in result:
                        struct = result['document_structure']
                        print(f"   Tables: {struct.get('tables_count', 0)}")
                        print(f"   Figures: {struct.get('figures_count', 0)}")
                        print(f"   Text items: {struct.get('text_items_count', 0)}")
                    
                    # Processing info
                    if 'processing_info' in result:
                        proc_info = result['processing_info']
                        print(f"   Server processing time: {proc_info.get('processing_time')}")
                        print(f"   Timeout used: {proc_info.get('timeout_used')}")
                        print(f"   Was problematic: {proc_info.get('was_problematic_file')}")
                        print(f"   OCR actually used: {proc_info.get('ocr_enabled')}")
                        print(f"   Status: {proc_info.get('conversion_status')}")
                    
                    # Save content
                    content = result.get('content', '')
                    if content:
                        output_file = f"NWDOH_parsed_{scenario['name'].replace(' ', '_').replace(',', '')}.md"
                        with open(output_file, 'w', encoding='utf-8') as out_f:
                            out_f.write(content)
                        print(f"💾 Saved to: {output_file}")
                        
                        # Show content preview
                        print(f"\n📝 First 300 characters:")
                        print("-" * 50)
                        print(content[:300])
                        if len(content) > 300:
                            print("...")
                        print("-" * 50)
                    
                    results.append({
                        'scenario': scenario['name'],
                        'success': True,
                        'total_time': total_time,
                        'content_length': len(content),
                        'pages': result.get('page_count', 0)
                    })
                    
                else:
                    print(f"❌ Failed with status: {response.status_code}")
                    try:
                        error_info = response.json()
                        print(f"   Error: {error_info.get('error', 'Unknown')}")
                        print(f"   Message: {error_info.get('message', 'No message')}")
                    except:
                        print(f"   Response: {response.text[:300]}")
                    
                    results.append({
                        'scenario': scenario['name'],
                        'success': False,
                        'error': response.text[:100]
                    })
                
        except requests.exceptions.Timeout:
            print("❌ Client request timed out (3 minutes)")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': 'Client timeout'
            })
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'error': str(e)
            })
    
    # Test 4: Summary
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ Successful tests: {len(successful_tests)}")
    print(f"❌ Failed tests: {len(failed_tests)}")
    
    if successful_tests:
        print("\n✅ Successful scenarios:")
        for result in successful_tests:
            print(f"   {result['scenario']}: {result['total_time']:.1f}s, "
                  f"{result['content_length']} chars, {result['pages']} pages")
    
    if failed_tests:
        print("\n❌ Failed scenarios:")
        for result in failed_tests:
            print(f"   {result['scenario']}: {result.get('error', 'Unknown error')}")
    
    if successful_tests:
        print(f"\n🎉 SUCCESS! The NWDOH document was parsed successfully!")
        print(f"   The timeout handling prevented the server from hanging.")
        print(f"   Check the generated .md files for the parsed content.")
    else:
        print(f"\n⚠️  All parsing attempts failed.")
        print(f"   This document may require special handling or different settings.")
    
    return len(successful_tests) > 0

if __name__ == "__main__":
    test_nwdoh_document()
