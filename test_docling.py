#!/usr/bin/env python3
"""
Test script to debug Docling document processing issues
"""

import sys
import traceback
from pathlib import Path

def test_basic_import():
    """Test if we can import docling modules"""
    try:
        from docling.document_converter import DocumentConverter
        print("✅ Successfully imported DocumentConverter")
        return True
    except Exception as e:
        print(f"❌ Failed to import DocumentConverter: {e}")
        traceback.print_exc()
        return False

def test_converter_initialization():
    """Test if we can initialize the converter"""
    try:
        from docling.document_converter import DocumentConverter
        converter = DocumentConverter()
        print("✅ Successfully initialized DocumentConverter")
        return converter
    except Exception as e:
        print(f"❌ Failed to initialize DocumentConverter: {e}")
        traceback.print_exc()
        return None

def test_simple_conversion():
    """Test a simple document conversion"""
    try:
        from docling.document_converter import DocumentConverter
        
        # Create a simple test file
        test_content = """# Test Document

This is a simple test document to verify that Docling is working properly.

## Section 1
Some content here.

## Section 2
More content here.
"""
        
        test_file = Path("test_document.md")
        test_file.write_text(test_content)
        
        converter = DocumentConverter()
        print("🔄 Converting test document...")
        
        result = converter.convert(str(test_file))
        
        if result and result.document:
            print("✅ Successfully converted test document")
            print(f"Status: {result.status}")
            print(f"Document has {len(result.document.pages)} pages")
            
            # Try to export to markdown
            markdown_content = result.document.export_to_markdown()
            print(f"Exported content length: {len(markdown_content)} characters")
            print("First 200 characters of exported content:")
            print(markdown_content[:200])
            
            # Cleanup
            test_file.unlink()
            return True
        else:
            print("❌ Conversion returned empty result")
            return False
            
    except Exception as e:
        print(f"❌ Failed to convert test document: {e}")
        traceback.print_exc()
        # Cleanup
        if test_file.exists():
            test_file.unlink()
        return False

def test_pdf_conversion():
    """Test PDF conversion capabilities"""
    try:
        from docling.document_converter import DocumentConverter
        from docling.datamodel.base_models import InputFormat
        from docling.datamodel.pipeline_options import PdfPipelineOptions
        from docling.document_converter import PdfFormatOption
        
        print("🔄 Testing PDF conversion setup...")
        
        # Test pipeline options
        pipeline_options = PdfPipelineOptions()
        pipeline_options.do_ocr = False
        pipeline_options.do_table_structure = True
        
        format_options = {
            InputFormat.PDF: PdfFormatOption(pipeline_options=pipeline_options)
        }
        
        converter = DocumentConverter(format_options=format_options)
        print("✅ Successfully created PDF converter with options")
        return True
        
    except Exception as e:
        print(f"❌ Failed to setup PDF conversion: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Docling Installation and Functionality")
    print("=" * 60)
    
    # Test 1: Basic imports
    print("\n1. Testing basic imports...")
    if not test_basic_import():
        print("❌ Basic import test failed. Exiting.")
        sys.exit(1)
    
    # Test 2: Converter initialization
    print("\n2. Testing converter initialization...")
    converter = test_converter_initialization()
    if converter is None:
        print("❌ Converter initialization failed. Exiting.")
        sys.exit(1)
    
    # Test 3: Simple conversion
    print("\n3. Testing simple document conversion...")
    if not test_simple_conversion():
        print("❌ Simple conversion test failed.")
    
    # Test 4: PDF setup
    print("\n4. Testing PDF conversion setup...")
    if not test_pdf_conversion():
        print("❌ PDF conversion setup failed.")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed. Check results above.")

if __name__ == "__main__":
    main()
