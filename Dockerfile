# Use the official Python image as a parent image
FROM python:3.11-slim

# Set the working directory in the container
WORKDIR /app

# Set environment variables
# Prevents Python from writing pyc files to disc
ENV PYTHONDONTWRITEBYTECODE 1
# Ensures Python output is sent straight to the terminal without buffering
ENV PYTHONUNBUFFERED 1
# Set the default artifacts path for Docling models
ENV DOCLING_ARTIFACTS_PATH /artifacts

# Install system dependencies (add any others your OCR engines might need)
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    libgl1-mesa-glx \
    build-essential \
    && apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry

# Copy only the dependency definition files to leverage Docker layer caching
COPY pyproject.toml poetry.lock ./

# Install project dependencies
# --no-root: Don't install the project itself, we'll copy it later
# --without dev: Don't install development-only dependencies
RUN poetry config virtualenvs.create false && \
    poetry install --no-root --without dev --all-extras && \
    pip install opencv-python pillow && \
    # Pre-download EasyOCR models to avoid runtime downloads
    python -c "import easyocr; reader = easyocr.Reader(['en'], gpu=False)"

# === NEW: Copy the API and application code ===
COPY docling/ /app/docling/
COPY api/ /app/api/

# === NEW: Expose the port the API will run on ===
EXPOSE 8081

# === NEW: The command to run when the container starts ===
# Runs the Uvicorn server, making it accessible from outside the container
CMD ["uvicorn", "api.main:app", "--host", "0.0.0.0", "--port", "8081"]