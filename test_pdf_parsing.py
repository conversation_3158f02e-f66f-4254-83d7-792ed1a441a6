#!/usr/bin/env python3
"""
Test PDF parsing specifically for the problematic file
"""

import requests
import json
import time
from pathlib import Path

def find_pdf_files():
    """Find PDF files in the current directory"""
    pdf_files = list(Path('.').glob('*.pdf'))
    return pdf_files

def test_pdf_parsing(pdf_file, enable_ocr=False):
    """Test PDF parsing with specific settings"""
    try:
        if not pdf_file.exists():
            print(f"❌ PDF file not found: {pdf_file}")
            return False
            
        file_size_mb = pdf_file.stat().st_size / (1024 * 1024)
        print(f"🔄 Testing PDF parsing: {pdf_file.name}")
        print(f"📄 File size: {file_size_mb:.1f} MB")
        print(f"👁️  OCR enabled: {enable_ocr}")
        
        with open(pdf_file, 'rb') as f:
            files = {'file': (pdf_file.name, f, 'application/pdf')}
            data = {
                'enable_ocr': str(enable_ocr).lower(),
                'enable_table_structure': 'true',
                'output_format': 'markdown'
            }
            
            print("⏳ Sending request to API...")
            start_time = time.time()
            
            response = requests.post(
                "http://localhost:8081/process-document/",
                files=files,
                data=data,
                timeout=120  # 2 minute timeout
            )
            
            processing_time = time.time() - start_time
            print(f"⏱️  Total request time: {processing_time:.2f} seconds")
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                
                print("✅ PDF parsing successful!")
                print(f"   Success: {result.get('success', 'Unknown')}")
                print(f"   Filename: {result.get('filename', 'Unknown')}")
                print(f"   Pages: {result.get('page_count', 0)}")
                
                # Document structure info
                if 'document_structure' in result:
                    struct = result['document_structure']
                    print(f"   Tables: {struct.get('tables_count', 0)}")
                    print(f"   Figures: {struct.get('figures_count', 0)}")
                    print(f"   Text items: {struct.get('text_items_count', 0)}")
                
                # Processing info
                if 'processing_info' in result:
                    proc_info = result['processing_info']
                    print(f"   Server processing time: {proc_info.get('processing_time', 'Unknown')}")
                    print(f"   OCR actually used: {proc_info.get('ocr_enabled', 'Unknown')}")
                    print(f"   Conversion status: {proc_info.get('conversion_status', 'Unknown')}")
                
                # Content info
                content = result.get('content', '')
                content_length = len(content)
                print(f"   Content length: {content_length} characters")
                
                if content:
                    # Show first part of content
                    print("\n📝 First 300 characters of parsed content:")
                    print("-" * 60)
                    print(content[:300])
                    if content_length > 300:
                        print("...")
                    print("-" * 60)
                    
                    # Save content to file
                    output_file = f"parsed_{pdf_file.stem}_ocr_{enable_ocr}.md"
                    with open(output_file, 'w', encoding='utf-8') as out_f:
                        out_f.write(content)
                    print(f"💾 Full content saved to: {output_file}")
                    
                    # Basic content analysis
                    lines = content.split('\n')
                    non_empty_lines = [line for line in lines if line.strip()]
                    print(f"📊 Content analysis:")
                    print(f"   Total lines: {len(lines)}")
                    print(f"   Non-empty lines: {len(non_empty_lines)}")
                    print(f"   Average line length: {content_length / len(lines):.1f} chars")
                
                return True
            else:
                print(f"❌ API returned error status: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"   Error: {error_info.get('error', 'Unknown error')}")
                    print(f"   Message: {error_info.get('message', 'No message')}")
                except:
                    print(f"   Response text: {response.text[:500]}")
                return False
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out - PDF processing took too long")
        return False
    except Exception as e:
        print(f"❌ PDF parsing test failed: {e}")
        return False

def test_health():
    """Test server health"""
    try:
        response = requests.get("http://localhost:8081/health", timeout=10)
        if response.status_code == 200:
            print("✅ Server is healthy")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def main():
    """Test PDF parsing functionality"""
    print("🧪 Testing PDF Document Parsing")
    print("=" * 60)
    
    # Test 1: Health check
    print("\n1. Testing server health...")
    if not test_health():
        print("❌ Server not healthy. Exiting.")
        return
    
    # Test 2: Find PDF files
    print("\n2. Looking for PDF files...")
    pdf_files = find_pdf_files()
    
    if not pdf_files:
        print("❌ No PDF files found in current directory")
        return
    
    for pdf_file in pdf_files:
        print(f"   Found: {pdf_file.name} ({pdf_file.stat().st_size / (1024*1024):.1f} MB)")
    
    # Test 3: Parse each PDF
    print(f"\n3. Testing PDF parsing...")
    
    for i, pdf_file in enumerate(pdf_files, 1):
        print(f"\n--- PDF Test {i}/{len(pdf_files)}: {pdf_file.name} ---")
        
        # Test without OCR first (faster and more reliable)
        print("\n🔸 Testing WITHOUT OCR:")
        success_no_ocr = test_pdf_parsing(pdf_file, enable_ocr=False)
        
        # Test with OCR only if file is small enough
        file_size_mb = pdf_file.stat().st_size / (1024 * 1024)
        if file_size_mb < 5:  # Only test OCR on files smaller than 5MB
            print("\n🔸 Testing WITH OCR:")
            success_with_ocr = test_pdf_parsing(pdf_file, enable_ocr=True)
        else:
            print(f"\n🔸 Skipping OCR test (file too large: {file_size_mb:.1f}MB)")
            success_with_ocr = None
        
        print(f"\n📊 Results for {pdf_file.name}:")
        print(f"   Without OCR: {'✅ Success' if success_no_ocr else '❌ Failed'}")
        if success_with_ocr is not None:
            print(f"   With OCR: {'✅ Success' if success_with_ocr else '❌ Failed'}")
        else:
            print(f"   With OCR: ⏭️  Skipped (large file)")
    
    print("\n" + "=" * 60)
    print("🏁 PDF parsing test completed!")

if __name__ == "__main__":
    main()
