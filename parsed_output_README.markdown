# Docling

arXiv

Docs

PyPI version

PyPI - Python Version

Poetry

Code style: black

Imports: isort

Pydantic v2

pre-commit

License MIT

PyPI Downloads

Docling Actor

OpenSSF Best Practices

LF AI &amp; Data

Docling simplifies document processing, parsing diverse formats — including advanced PDF understanding — and providing seamless integrations with the gen AI ecosystem.

## Features

- 🗂️ Parsing of
- 📑 Advanced PDF understanding incl. page layout, reading order, table structure, code, formulas, image classification, and more
- 🧬 Unified, expressive
- ↪️ Various
- 🔒 Local execution capabilities for sensitive data and air-gapped environments
- 🤖 Plug-and-play
- 🔍 Extensive OCR support for scanned PDFs and images
- 🥚 Support of Visual Language Models (
- 💻 Simple and convenient CLI

### Coming soon

- 📝 Metadata extraction, including title, authors, references &amp; language
- 📝 Chart understanding (Barchart, Piechart, LinePlot, etc)
- 📝 Complex chemistry understanding (Molecular structures)

## Installation

To use Docling, simply install

```
docling
```

from your package manager, e.g. pip:

```
pip install docling
```

Works on macOS, Linux and Windows environments. Both x86\_64 and arm64 architectures.

More  detailed installation instructions  are available in the docs.

## Getting started

To convert individual documents with python, use

```
convert()
```

, for example:

```
from docling.document_converter import DocumentConverter

source = "https://arxiv.org/pdf/2408.09869"  # document per local path or URL
converter = DocumentConverter()
result = converter.convert(source)
print(result.document.export_to_markdown())  # output: "## Docling Technical Report[...]"
```

More  advanced usage options  are available in the docs.

## CLI

Docling has a built-in CLI to run conversions.

```
docling https://arxiv.org/pdf/2206.01062
```

You can also use 🥚 SmolDocling  and other VLMs via Docling CLI:

```
docling --pipeline vlm --vlm-model smoldocling https://arxiv.org/pdf/2206.01062
```

This will use MLX acceleration on supported Apple Silicon hardware.

Read more  here

## Documentation

Check out Docling's  documentation , for details on installation, usage, concepts, recipes, extensions, and more.

## Examples

Go hands-on with our  examples , demonstrating how to address different application use cases with Docling.

## Integrations

To further accelerate your AI application development, check out Docling's native integrations  with popular frameworks and tools.

## Get help and support

Please feel free to connect with us using the  discussion section .

## Technical report

For more details on Docling's inner workings, check out the  Docling Technical Report .

## Contributing

Please read  Contributing to Docling  for details.

## References

If you use Docling in your projects, please consider citing the following:

```
@techreport{Docling,
  author = {Deep Search Team},
  month = {8},
  title = {Docling Technical Report},
  url = {https://arxiv.org/abs/2408.09869},
  eprint = {2408.09869},
  doi = {10.48550/arXiv.2408.09869},
  version = {1.0.0},
  year = {2024}
}
```

## License

The Docling codebase is under MIT license. For individual model usage, please refer to the model licenses found in the original packages.

## LF AI &amp; Data

Docling is hosted as a project in the  LF AI &amp; Data Foundation .

### IBM ❤️ Open Source AI

The project was started by the AI for knowledge team at IBM Research Zurich.