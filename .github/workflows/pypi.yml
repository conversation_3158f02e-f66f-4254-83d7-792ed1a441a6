name: "Build and publish package"

on:
  release:
    types: [published]

permissions:
  contents: read

env:
  # disable keyring (https://github.com/actions/runner-images/issues/6185):
  PYTHON_KEYRING_BACKEND: keyring.backends.null.Keyring

jobs:
  build-and-publish:
    runs-on: ubuntu-latest
    environment:
      name: pypi
      url: https://pypi.org/p/docling
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/setup-poetry
      - name: Build and publish
        run: poetry build
      - name: Publish distribution 📦 to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          attestations: true
