name: "Run CD"

on:
  workflow_dispatch:

env:
  # disable keyring (https://github.com/actions/runner-images/issues/6185):
  PYTHON_KEYRING_BACKEND: keyring.backends.null.Keyring

jobs:
  code-checks:
    uses: ./.github/workflows/checks.yml
    with:
      push_coverage: false
  pre-release-check:
    runs-on: ubuntu-latest
    outputs:
      TARGET_TAG_V: ${{ steps.version_check.outputs.TRGT_VERSION }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # for fetching tags, required for semantic-release
      - uses: ./.github/actions/setup-poetry
      - name: Check version of potential release
        id: version_check
        run: |
            TRGT_VERSION=$(poetry run semantic-release print-version)
            echo "TRGT_VERSION=${TRGT_VERSION}" >> $GITHUB_OUTPUT
            echo "${TRGT_VERSION}"
      - name: Check notes of potential release
        run: poetry run semantic-release changelog --unreleased
  release:
    needs: [code-checks, pre-release-check]
    if: needs.pre-release-check.outputs.TARGET_TAG_V != ''
    environment: auto-release
    runs-on: ubuntu-latest
    concurrency: release
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ vars.CI_APP_ID }}
          private-key: ${{ secrets.CI_PRIVATE_KEY }}
      - uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}
          fetch-depth: 0  # for fetching tags, required for semantic-release
      - uses: ./.github/actions/setup-poetry
      - name: Run release script
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
          TARGET_VERSION: ${{ needs.pre-release-check.outputs.TARGET_TAG_V }}
          CHGLOG_FILE: CHANGELOG.md
        run: ./.github/scripts/release.sh
        shell: bash
