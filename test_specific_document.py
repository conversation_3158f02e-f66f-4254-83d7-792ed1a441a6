#!/usr/bin/env python3
"""
Test script for the specific RE-ADVERT NWDOH 05 2025 document
"""

import requests
import json
import time
import os
from pathlib import Path

def find_target_document():
    """Find the RE-ADVERT NWDOH document"""
    # Common patterns for the filename
    patterns = [
        "*NWDOH*",
        "*advert*", 
        "*ADVERT*",
        "*2025*",
        "RE-ADVERT*",
        "*RE-ADVERT*"
    ]
    
    found_files = []
    
    # Search in current directory and common locations
    search_paths = [
        ".",
        "~/Downloads",
        "~/Desktop", 
        "~/Documents"
    ]
    
    for search_path in search_paths:
        path = Path(search_path).expanduser()
        if path.exists():
            for pattern in patterns:
                files = list(path.glob(pattern))
                for file in files:
                    if file.suffix.lower() == '.pdf':
                        found_files.append(file)
    
    return found_files

def test_document_parsing(file_path, test_scenarios=None):
    """Test document parsing with different scenarios"""
    if test_scenarios is None:
        test_scenarios = [
            {"ocr": False, "table_structure": True, "format": "markdown"},
            {"ocr": False, "table_structure": False, "format": "markdown"},
            {"ocr": True, "table_structure": True, "format": "markdown"},
        ]
    
    file_path = Path(file_path)
    if not file_path.exists():
        print(f"❌ File not found: {file_path}")
        return False
    
    file_size_mb = file_path.stat().st_size / (1024 * 1024)
    print(f"🔄 Testing document: {file_path.name}")
    print(f"📄 File size: {file_size_mb:.2f} MB")
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Scenario {i}/{len(test_scenarios)} ---")
        print(f"OCR: {scenario['ocr']}, Table Structure: {scenario['table_structure']}, Format: {scenario['format']}")
        
        # Skip OCR for large files to prevent timeout
        if file_size_mb > 2 and scenario['ocr']:
            print("⚠️  Skipping OCR for large file to prevent timeout")
            continue
        
        try:
            with open(file_path, 'rb') as f:
                files = {'file': (file_path.name, f, 'application/pdf')}
                data = {
                    'enable_ocr': str(scenario['ocr']).lower(),
                    'enable_table_structure': str(scenario['table_structure']).lower(),
                    'output_format': scenario['format']
                }
                
                print("⏳ Sending request...")
                start_time = time.time()
                
                response = requests.post(
                    "http://localhost:8081/process-document/",
                    files=files,
                    data=data,
                    timeout=300  # 5 minute timeout for large documents
                )
                
                processing_time = time.time() - start_time
                print(f"⏱️  Total time: {processing_time:.2f} seconds")
                print(f"📊 Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    
                    print("✅ Parsing successful!")
                    print(f"   Pages: {result.get('page_count', 0)}")
                    print(f"   Content length: {len(result.get('content', ''))}")
                    
                    # Document structure
                    if 'document_structure' in result:
                        struct = result['document_structure']
                        print(f"   Tables: {struct.get('tables_count', 0)}")
                        print(f"   Figures: {struct.get('figures_count', 0)}")
                        print(f"   Text items: {struct.get('text_items_count', 0)}")
                    
                    # Processing info
                    if 'processing_info' in result:
                        proc_info = result['processing_info']
                        print(f"   Server processing time: {proc_info.get('processing_time')}")
                        print(f"   Conversion status: {proc_info.get('conversion_status')}")
                    
                    # Save content
                    content = result.get('content', '')
                    if content:
                        scenario_name = f"ocr_{scenario['ocr']}_tables_{scenario['table_structure']}"
                        output_file = f"parsed_NWDOH_{scenario_name}.{scenario['format']}"
                        with open(output_file, 'w', encoding='utf-8') as out_f:
                            out_f.write(content)
                        print(f"💾 Saved to: {output_file}")
                        
                        # Show content preview
                        print(f"\n📝 First 300 characters:")
                        print("-" * 50)
                        print(content[:300])
                        if len(content) > 300:
                            print("...")
                        print("-" * 50)
                    
                    results.append({
                        'scenario': scenario,
                        'success': True,
                        'processing_time': processing_time,
                        'content_length': len(content),
                        'pages': result.get('page_count', 0)
                    })
                    
                else:
                    print(f"❌ Failed with status: {response.status_code}")
                    try:
                        error_info = response.json()
                        print(f"   Error: {error_info.get('error', 'Unknown')}")
                        print(f"   Message: {error_info.get('message', 'No message')}")
                    except:
                        print(f"   Response: {response.text[:300]}")
                    
                    results.append({
                        'scenario': scenario,
                        'success': False,
                        'error': response.text[:100]
                    })
                
        except requests.exceptions.Timeout:
            print("❌ Request timed out")
            results.append({
                'scenario': scenario,
                'success': False,
                'error': 'Timeout'
            })
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append({
                'scenario': scenario,
                'success': False,
                'error': str(e)
            })
    
    return results

def test_server_health():
    """Test if server is healthy"""
    try:
        response = requests.get("http://localhost:8081/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print("✅ Server is healthy")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Converter available: {health_data.get('docling_available')}")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Testing RE-ADVERT NWDOH 05 2025 Document")
    print("=" * 60)
    
    # Test 1: Server health
    print("\n1. Checking server health...")
    if not test_server_health():
        print("❌ Server not healthy. Please ensure the server is running.")
        return
    
    # Test 2: Find the document
    print("\n2. Looking for RE-ADVERT NWDOH document...")
    found_files = find_target_document()
    
    if found_files:
        print("Found potential files:")
        for file in found_files:
            print(f"   📄 {file}")
        
        # Use the first found file
        target_file = found_files[0]
    else:
        print("❌ RE-ADVERT NWDOH document not found automatically.")
        print("\nPlease provide the full path to the document:")
        file_path = input("Enter file path: ").strip()
        
        if not file_path:
            print("❌ No file path provided. Exiting.")
            return
        
        target_file = Path(file_path)
        if not target_file.exists():
            print(f"❌ File not found: {target_file}")
            return
    
    # Test 3: Parse the document
    print(f"\n3. Testing document parsing...")
    results = test_document_parsing(target_file)
    
    # Test 4: Summary
    print("\n" + "=" * 60)
    print("📊 RESULTS SUMMARY")
    print("=" * 60)
    
    successful_tests = [r for r in results if r['success']]
    failed_tests = [r for r in results if not r['success']]
    
    print(f"✅ Successful tests: {len(successful_tests)}")
    print(f"❌ Failed tests: {len(failed_tests)}")
    
    if successful_tests:
        print("\n✅ Successful scenarios:")
        for result in successful_tests:
            scenario = result['scenario']
            print(f"   OCR: {scenario['ocr']}, Tables: {scenario['table_structure']} - "
                  f"{result['processing_time']:.1f}s, {result['content_length']} chars, "
                  f"{result['pages']} pages")
    
    if failed_tests:
        print("\n❌ Failed scenarios:")
        for result in failed_tests:
            scenario = result['scenario']
            print(f"   OCR: {scenario['ocr']}, Tables: {scenario['table_structure']} - "
                  f"Error: {result.get('error', 'Unknown')}")
    
    if successful_tests:
        print(f"\n🎉 Document parsing is working! Check the generated files for results.")
    else:
        print(f"\n⚠️  All parsing attempts failed. Check server logs for details.")

if __name__ == "__main__":
    main()
