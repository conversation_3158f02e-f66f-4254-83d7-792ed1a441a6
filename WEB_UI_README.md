# Docling Web UI

A modern, interactive web interface for the Docling document processing system. This web UI provides an easy-to-use interface for uploading, processing, and analyzing documents using the powerful Docling SDK.

## 🌟 Features

### Document Processing
- **Multi-format Support**: PDF, DOCX, PPTX, XLSX, HTML, Markdown, CSV, and Images
- **Advanced OCR**: Optional optical character recognition for scanned documents
- **Table Structure Detection**: Automatic table structure recognition and extraction
- **Multiple Output Formats**: Markdown, HTML, and JSON export options

### User Interface
- **Drag & Drop Upload**: Easy file upload with visual feedback
- **Real-time Processing**: Live progress indicators and status updates
- **Interactive Results**: Copy, download, and format switching capabilities
- **Document Statistics**: Automatic analysis of pages, tables, figures, and text items
- **Responsive Design**: Works seamlessly on desktop and mobile devices

### Technical Features
- **RESTful API**: Full API access with automatic documentation
- **Health Monitoring**: Built-in system health checks
- **Error Handling**: Comprehensive error reporting and user feedback
- **Performance Optimized**: Efficient processing with progress tracking

## 🚀 Quick Start

### Prerequisites
- Python 3.9 or higher
- Poetry (for dependency management)
- All Docling dependencies installed

### Installation

1. **Install Dependencies**
   ```bash
   # Install Jinja2 for templating (if not already installed)
   poetry add jinja2
   
   # Or using pip
   pip install jinja2
   ```

2. **Start the Web UI**
   ```bash
   # Using the startup script (recommended)
   python start_web_ui.py
   
   # Or directly with uvicorn
   uvicorn api.main:app --host 0.0.0.0 --port 8081 --reload
   ```

3. **Access the Interface**
   - Web UI: http://localhost:8081
   - API Documentation: http://localhost:8081/docs
   - Health Check: http://localhost:8081/health

## 📖 Usage Guide

### Basic Document Processing

1. **Upload Document**
   - Click "Select Document" or drag & drop a file
   - Supported formats: PDF, DOCX, PPTX, XLSX, HTML, MD, CSV, PNG, JPG, JPEG

2. **Configure Options**
   - **Enable OCR**: For scanned documents or images with text
   - **Table Structure Detection**: Automatically detect and extract table structures
   - **Output Format**: Choose between Markdown, HTML, or JSON

3. **Process Document**
   - Click "Process Document" to start conversion
   - Monitor progress with the real-time progress bar
   - View results in the right panel

4. **Review Results**
   - View extracted content in your chosen format
   - Check document statistics (pages, tables, figures, text items)
   - Copy content to clipboard or download as file

### Advanced Features

#### API Access
The web UI exposes a full RESTful API:

```bash
# Process a document via API
curl -X POST "http://localhost:8081/process-document/" \
     -H "accept: application/json" \
     -H "Content-Type: multipart/form-data" \
     -F "file=@document.pdf" \
     -F "enable_ocr=false" \
     -F "enable_table_structure=true" \
     -F "output_format=markdown"
```

#### Health Monitoring
```bash
# Check system health
curl http://localhost:8081/health
```

#### Supported Formats
```bash
# Get list of supported formats
curl http://localhost:8081/api/formats
```

## 🔧 Configuration

### Environment Variables
- `DOCLING_ARTIFACTS_PATH`: Path for model artifacts (default: `/artifacts`)
- `PYTHONPATH`: Ensure the project root is in the Python path

### Processing Options
- **OCR Settings**: Enable/disable optical character recognition
- **Table Detection**: Control table structure analysis
- **Output Formats**: Markdown, HTML, or JSON export
- **Accelerator**: CPU-based processing (configurable for GPU)

## 📁 File Structure

```
api/
├── main.py              # FastAPI application
├── static/
│   ├── style.css        # UI styling
│   └── script.js        # Frontend JavaScript
└── templates/
    └── index.html       # Main UI template

start_web_ui.py          # Startup script
WEB_UI_README.md         # This documentation
```

## 🛠️ Development

### Running in Development Mode
```bash
# Start with auto-reload
python start_web_ui.py

# Or with uvicorn directly
uvicorn api.main:app --host 0.0.0.0 --port 8081 --reload
```

### Customization
- **Styling**: Modify `api/static/style.css`
- **Functionality**: Update `api/static/script.js`
- **Layout**: Edit `api/templates/index.html`
- **API**: Extend `api/main.py`

## 🐳 Docker Deployment

The existing Dockerfile has been updated to support the web UI:

```bash
# Build the image
docker build -t docling-web-ui .

# Run the container
docker run -p 8081:8081 docling-web-ui

# Or use docker-compose (update port mapping)
docker-compose up
```

## 🔍 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using port 8081
   lsof -i :8081
   
   # Kill the process or use a different port
   uvicorn api.main:app --host 0.0.0.0 --port 8082
   ```

2. **Import Errors**
   ```bash
   # Ensure you're in the project root directory
   cd /path/to/docling
   python start_web_ui.py
   ```

3. **Missing Dependencies**
   ```bash
   # Install missing packages
   poetry install
   # or
   pip install fastapi uvicorn jinja2 python-multipart
   ```

4. **File Upload Issues**
   - Check file size limits
   - Verify file format is supported
   - Ensure sufficient disk space

### Performance Tips
- Enable table structure detection only when needed
- Use OCR sparingly for better performance
- Process smaller documents for faster results
- Monitor system resources during processing

## 📊 API Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/` | GET | Main web interface |
| `/process-document/` | POST | Process uploaded document |
| `/health` | GET | System health check |
| `/api/formats` | GET | Supported formats list |
| `/docs` | GET | API documentation |

## 🤝 Contributing

To contribute to the web UI:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This web UI is part of the Docling project and follows the same MIT license.

---

**Happy Document Processing! 🚀**
